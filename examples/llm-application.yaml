apiVersion: system.hero.ai/v1alpha1
kind: Application
metadata:
  name: llm-predict-v2
  namespace: llm-example
spec:
  version: v1
  serviceMesh:
    backend: istio
    subRoute:
      retries:
        autoRetire: true
        attempts: 3 # 重试次数
        perTryTimeout: 10s  # 每次重试的超时时间
      trafficPolicy:
        loadBalancer: 4 # 0: default 2 随机策略 4:轮训策略
        circuitBreaker:
          maxConnections: 100 # 每个连接的最大请求数
          http1MaxPendingRequests: 100  # 到目的地的最大待处理HTTP/1.1请求数
          consecutiveErrors: 5  # 连续多少个误则触发熔断
          interval: 1s # 统计错误数的时间窗口
          baseEjectionTime: 5m # 熔断后多久自动恢复
          maxEjectionPercent: 100 # 最大熔断比例
  autoScaling:
    backend: keda
    minReplicas: 1
    maxReplicas: 10
    cooldownPeriod: 600
    triggers:
      - type: cpu  #cpu利用率
        metadata:
          type: Utilization
          value: "50"
      - type: memory  #内存使用量
        metadata:
          type: AverageValue
          value: "536870912"
      - type: prometheus  #请求速率
        metadata:
          metricName: http_requests_total
          threshold: '20'
      - type: prometheus
        metadata:
          metricName: gpu  #gpu相关指标
          threshold: '2'   #2%
      - type: prometheus
        metadata:
          metricName: gpu_mem_usage   #gpu显存相关指标
          threshold: '1048576000'  #1000Mi
    serverless:  #开启serverless
      - type: prometheus
        metadata:
          metricName: http_requests_total
          threshold: '20'
  scheduler:
    schedulerName: default
  gateway:
    backend: istio
    host: llm-predict  # 同app名
  servers:
    image-describe:
      - version: v1
        replicas: 1
        serviceMesh:
          subRoute:
            weight: 100
        template:
          metadata:
            labels:
              app: image-describe
          spec:
            containers:
              - name: image-describe
                image: registry.cn-hangzhou.aliyuncs.com/ruanxingbaozi/pytorch:image_recognition_demo
                imagePullPolicy: Always
                args:
                  - bash
                  - -c
                  - python image_recognition_server.py
                ports:
                  - name: http
                    containerPort: 5000
                env:
                  - name: DEMO_GREETING
                    value: "Hello from the environment"
                resources:
                  limits:
                    cpu: "1"
                    memory: 4Gi
                    #nvidia.com/nvidia-rtx-3090: "1"
                  requests:
                    cpu: "1"
                    memory: 4Gi
                    #nvidia.com/nvidia-rtx-3090: "1"
    image-predict:
      - version: v1
        replicas: 1
        serviceMesh:
          subRoute:
            weight: 100
        template:
          metadata:
            labels:
              app: image-predict
          spec:
            containers:
              - name: image-predict
                image: registry.cn-hangzhou.aliyuncs.com/ruanxingbaozi/pytorch:image_recognition_demo
                imagePullPolicy: Always
                args:
                  - bash
                  - -c
                  - python image_classification_server.py
                ports:
                  - name: http
                    containerPort: 5000
                resources:
                  limits:
                    cpu: "1"
                    memory: 1Gi
                  requests:
                    cpu: "1"
                    memory: 1Gi
    llm-predict:
      - version: v1
        replicas: 1
        serviceMesh:
          subRoute:
            weight: 100
        template:
          metadata:
            labels:
              app: llm-predict
          spec:
            containers:
              - name: llm-predict
                image: registry.cn-hangzhou.aliyuncs.com/ruanxingbaozi/pytorch:image_recognition_demo
                imagePullPolicy: Always
                args:
                  - bash
                  - -c
                  - python llm_server.py
                ports:
                  - name: http
                    containerPort: 5000
                resources:
                  limits:
                    cpu: "1"
                    memory: 1Gi
                  requests:
                    cpu: "1"
                    memory: 1Gi
    llm-inference-app:
      - version: v1
        replicas: 1
        serviceMesh:
          backend: istio
          ingressGateway: true
          subRoute:
            weight: 100
            match:
            - url:
                prefix: "/v1"
              headers:
                X-User-Agent-Key:
                  exact: "sdsdakjsakljsakldjskldajklsdjklsadjl"  # token值
            retries:
              autoRetire: true
              attempts: 3
        template:
          metadata:
            labels:
              app: llm-inference-app
          spec:
            containers:
              - name: llm-inference-app
                image: registry.cn-hangzhou.aliyuncs.com/ruanxingbaozi/pytorch:image_recognition_demo
                imagePullPolicy: Always
                args:
                  - bash
                  - -c
                  - python app.py
                ports:
                  - name: http
                    containerPort: 7860
                resources:
                  limits:
                    cpu: "1"
                    memory: 1Gi
                  requests:
                    cpu: "1"
                    memory: 1Gi