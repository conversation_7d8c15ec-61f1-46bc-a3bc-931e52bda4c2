apiVersion: system.hero.ai/v1alpha1
kind: Application
metadata:
  # annotations:
  #  application.leinao.ai/action: stop  
  name: llm-predict
  namespace: llm-example
spec:
  version: v1
  serviceMesh:
    backend: istio
    subRoute:
      weight: 100
      retries:
        autoRetire: true
        attempts: 3 # 重试次数

  volumes:
    - mountPath: "/data"
      volumeClaimName: "my-data-pvc"
            
  autoScaling:
    backend: keda
    minReplicas: 1
    maxReplicas: 10
    triggers:
      - type: cpu
        metadata:
          type: Utilization
          value: "50"
      - type: memory
        metadata:
          type: Utilization
          value: "1024"
      - type: prometheus
        metadata:
          metricName: http_requests_total
          threshold: '20'
    serverless:
      - type: memory
        metadata:
          type: Utilization
          value: "1024"
  scheduler:
    schedulerName: default
  gateway:
    backend: istio
    host: llm-predict.demo.hero.ai
  servers:
    image-describe:
      - version: v1
        replicas: 2
        template:
          metadata:
            labels:
              app: image-describe
          spec:
            volumes:
              - name: my-volume
                persistentVolumeClaim:
                  claimName: my-pvc
            containers:
              - name: image-describe
                image: registry.cn-hangzhou.aliyuncs.com/ruanxingbaozi/pytorch:image_recognition_demo
                imagePullPolicy: Always
                volumeMounts:
                  - name: my-volume
                    mountPath: /path/in/container
                args:
                  - bash
                  - -c
                  - python image_recognition_server.py
                ports:
                  - name: http
                    containerPort: 5000
                env:
                  - name: DEMO_GREETING
                    value: "Hello from the environment"
                resources:
                  limits:
                    cpu: "4"
                    memory: 4Gi
                    #nvidia.com/nvidia-rtx-3090: "1"
                  requests:
                    cpu: "4"
                    memory: 4Gi
                    #nvidia.com/nvidia-rtx-3090: "1"

              
        serviceMesh:
          backend: istio
          ingressGateway: true
          subRoute:
            weight: 100
            match:
            - headers:
                end-user:
                  exact: application
              uri:
                prefix: /application/v1
            retries:
              autoRetire: true
              attempts: 3 # 重试次数
        autoScaling:
          backend: keda
          minReplicas: 1
          maxReplicas: 10
          triggers:
            - type: cpu
              metadata:
                type: Utilization
                value: "50"
            - type: memory
              metadata:
                type: Utilization
                value: "1024"
            - type: prometheus
              metadata:
                metricName: http_requests_total
                threshold: '20'
          serverless:
            - type: memory
              metadata:
                type: Utilization
                value: "1024"

status:
  availableReplicas: 5
  availableServers: 4
  phase: Running
  replicas: 5
  serverStates:
    image-describe:
      availableReplicas: 1
      replicas: 1
    image-predict:
      availableReplicas: 2
      replicas: 2
    llm-inference-app:
      availableReplicas: 1
      replicas: 1
    llm-predict:
      availableReplicas: 1
      replicas: 1