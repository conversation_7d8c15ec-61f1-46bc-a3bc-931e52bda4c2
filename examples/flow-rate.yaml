apiVersion: system.hero.ai/v1alpha1
kind: Application
metadata:
  name: flow-rate
spec:
  version: v1
  scheduler:
    schedulerName: default
  gateway:
    backend: istio
    host: flow-rate.***********.nip.io
  servers:
    productpage:
      - version: v1
        replicas: 1
        serviceMesh:
          backend: istio
          ingressGateway: true
          subRoute:
            weight: 100
            match:
            - uri:
                exact: /productpage
            - uri:
                prefix: /static
            - uri:
                exact: /login
            - uri:
                exact: /logout
            - uri:
                prefix: /api/v1/products
        template:
          metadata:
            labels:
              app: productpage
          spec:
            containers:
              - name: productpage
                image: docker.io/istio/examples-bookinfo-productpage-v1:1.18.0
                imagePullPolicy: IfNotPresent
                ports:
                  - containerPort: 9080
                volumeMounts:
                - name: tmp
                  mountPath: /tmp
            volumes:
            - name: tmp
              emptyDir: {}
    details:
      - version: v1
        replicas: 1
        serviceMesh:
          subRoute:
            weight: 100
        template:
          metadata:
            labels:
              app: details
          spec:
            containers:
              - name: details
                image: docker.io/istio/examples-bookinfo-details-v1:1.18.0
                imagePullPolicy: IfNotPresent
                ports:
                  - containerPort: 9080
    ratings:
      - version: v1
        replicas: 1
        serviceMesh:
          subRoute:
            weight: 100
        template:
          metadata:
            labels:
              app: ratings
          spec:
            containers:
              - name: ratings
                image: docker.io/istio/examples-bookinfo-ratings-v1:1.18.0
                imagePullPolicy: IfNotPresent
                ports:
                  - containerPort: 9080
    reviews:
      - version: v1
        replicas: 1
        serviceMesh:
          subRoute:
            weight: 33
            match:
              - headers:
                  end-user:
                    exact: "jason"
        template:
          metadata:
            labels:
              app: reviews
          spec:
            containers:
              - name: reviews
                image: docker.io/istio/examples-bookinfo-reviews-v1:1.18.0
                imagePullPolicy: IfNotPresent
                env:
                  - name: LOG_DIR
                    value: "/tmp/logs"
                ports:
                  - containerPort: 9080
                volumeMounts:
                  - name: tmp
                    mountPath: /tmp
                  - name: wlp-output
                    mountPath: /opt/ibm/wlp/output
            volumes:
              - name: wlp-output
                emptyDir: {}
              - name: tmp
                emptyDir: {}
      - version: v2
        replicas: 1
        serviceMesh:
          subRoute:
            weight: 33
        template:
          metadata:
            labels:
              app: reviews
          spec:
            containers:
              - name: reviews
                image: docker.io/istio/examples-bookinfo-reviews-v2:1.18.0
                imagePullPolicy: IfNotPresent
                env:
                  - name: LOG_DIR
                    value: "/tmp/logs"
                ports:
                  - containerPort: 9080
                volumeMounts:
                  - name: tmp
                    mountPath: /tmp
                  - name: wlp-output
                    mountPath: /opt/ibm/wlp/output
            volumes:
              - name: wlp-output
                emptyDir: {}
              - name: tmp
                emptyDir: {}
      - version: v3
        replicas: 1
        serviceMesh:
          subRoute:
            weight: 34
        template:
          metadata:
            labels:
              app: reviews
          spec:
            containers:
              - name: reviews
                image: docker.io/istio/examples-bookinfo-reviews-v3:1.18.0
                imagePullPolicy: IfNotPresent
                env:
                  - name: LOG_DIR
                    value: "/tmp/logs"
                ports:
                  - containerPort: 9080
                volumeMounts:
                  - name: tmp
                    mountPath: /tmp
                  - name: wlp-output
                    mountPath: /opt/ibm/wlp/output
            volumes:
              - name: wlp-output
                emptyDir: { }
              - name: tmp
                emptyDir: { }



# 部署后访问 flow-rate.***********.nip.io:12534/productpage多次刷新页面，显示不同颜色星星，只在没星星和红色星之间刷新，，使用jason登陆后只显示黑色星星
# 测试点 流量按比例分发，测试match的 匹配规则，如productpage的match.url和reviews v2版本的match.headers