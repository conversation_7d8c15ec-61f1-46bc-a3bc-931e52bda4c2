apiVersion: system.hero.ai/v1alpha1
kind: Application
metadata:
  annotations:
  finalizers:
    - application.leinao.ai/finalizer
  name: app-codeserver
spec:
  gateway:
    backend: istio
    host: code-server.***********.nip.io
  scheduler:
    schedulerName: default
  servers:
    code-server:
      - replicas: 1
        scheduler: {}
        serviceMesh:
          autoMTLS: true
          backend: istio
          ingressGateway: true
          subRoute:
            weight: 100
        template:
          metadata:
            labels:
              app: code-server
          spec:
            containers:
              - args:
                  - bash
                  - -c
                  - code-server --host 0.0.0.0 --port 5000 --auth none
                image: registry.cn-hangzhou.aliyuncs.com/ruanxingbaozi/pytorch:image_recognition_demo
                imagePullPolicy: Always
                name: code-server
                ports:
                  - containerPort: 5000
                    name: http
                    protocol: TCP
                resources:
                  limits:
                    cpu: "1"
                    memory: 1Gi
                  requests:
                    cpu: "1"
                    memory: 1Gi
        version: v1
    llm-predict:
      - autoScaling: {}
        replicas: 1
        scheduler: {}
        serviceMesh:
          autoMTLS: true
          backend: istio
          subRoute:
            weight: 100
        template:
          metadata:
            labels:
              app: llm-predict
          spec:
            containers:
              - args:
                  - bash
                  - -c
                  - python llm_server.py
                env:
                  - name: LLM_VERSION
                    value: v1
                image: registry.cn-hangzhou.aliyuncs.com/ruanxingbaozi/pytorch:image_recognition_demo
                imagePullPolicy: Always
                name: llm-predict
                ports:
                  - containerPort: 5000
                    name: http
                    protocol: TCP
                resources:
                  limits:
                    cpu: "1"
                    memory: 1Gi
                  requests:
                    cpu: "1"
                    memory: 1Gi
        version: v1
  version: v1