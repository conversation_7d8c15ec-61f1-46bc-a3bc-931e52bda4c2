apiVersion: system.hero.ai/v1alpha1
kind: Application
metadata:
  name: llm-predict-v2
  namespace: llm-example
spec:
  version: v1
  deploymentMode: Online  # serverless   如果是在线推理online的话，没有开启弹性伸缩，autoScaling的配置会被忽略,应用可以不传
  autoScaling:
    minReplicas: 1
    maxReplicas: 10
    cooldownPeriodSeconds: 600 # 扩缩容冷却时间 (秒)
    scalingTriggers:
      cpuUtil: 50   # CPU 利用率目标百分比
      memAvg: "512Mi"  "512Mi" # 平均内存使用量目标 (简化单位)
      reqSec: 20 # 每秒请求数目标
      gpuUtil: 2   # GPU 利用率目标百分比
      gpuMem: "1Gi" # GPU 显存使用量目标 (简化单位)
  scheduler:
    queue: default
  servers:
    llm-inference-app:
      - version: v1
        replicas: 1
        serviceMesh:
          ingressGateway: true
          subRoute:
            weight: 100
            timeoutSeconds: 10
            match:
              - url:
                  prefix: "/v1"
                headers:
                  X-User-Agent-Key:
                    exact: "sdsdakjsakljsakldjskldajklsdjklsadjl"  # token值
                    retries:
                      autoRetire: true
                      attempts: 3
        template:
          metadata:
            labels:
              app: llm-inference-app
          spec:
            containers:
              - name: llm-inference-app
                image: registry.cn-hangzhou.aliyuncs.com/ruanxingbaozi/pytorch:image_recognition_demo
                imagePullPolicy: Always
                args:
                  - bash
                  - -c
                  - python app.py
                ports:
                  - name: http
                    containerPort: 7860
                resources:
                  limits:
                    cpu: "1"
                    memory: 1Gi
                  requests:
                    cpu: "1"
                    memory: 1Gi
    image-describe:
      - version: v1
        replicas: 1
        serviceMesh:
          subRoute:
            weight: 100
        template:
          metadata:
            labels:
              app: image-describe
          spec:
            containers:
              - name: image-describe
                image: registry.cn-hangzhou.aliyuncs.com/ruanxingbaozi/pytorch:image_recognition_demo
                imagePullPolicy: Always
                args:
                  - bash
                  - -c
                  - python image_recognition_server.py
                ports:
                  - name: http
                    containerPort: 5000
                env:
                  - name: DEMO_GREETING
                    value: "Hello from the environment"
                resources:
                  limits:
                    cpu: "1"
                    memory: 4Gi
                    #nvidia.com/nvidia-rtx-3090: "1"
                  requests:
                    cpu: "1"
                    memory: 4Gi
                    #nvidia.com/nvidia-rtx-3090: "1"
    image-predict:
      - version: v1
        replicas: 1
        serviceMesh:
          subRoute:
            weight: 100
        template:
          metadata:
            labels:
              app: image-predict
          spec:
            containers:
              - name: image-predict
                image: registry.cn-hangzhou.aliyuncs.com/ruanxingbaozi/pytorch:image_recognition_demo
                imagePullPolicy: Always
                args:
                  - bash
                  - -c
                  - python image_classification_server.py
                ports:
                  - name: http
                    containerPort: 5000
                resources:
                  limits:
                    cpu: "1"
                    memory: 1Gi
                  requests:
                    cpu: "1"
                    memory: 1Gi
    llm-predict:
      - version: v1
        replicas: 1
        serviceMesh:
          subRoute:
            weight: 100
        template:
          metadata:
            labels:
              app: llm-predict
          spec:
            containers:
              - name: llm-predict
                image: registry.cn-hangzhou.aliyuncs.com/ruanxingbaozi/pytorch:image_recognition_demo
                imagePullPolicy: Always
                args:
                  - bash
                  - -c
                  - python llm_server.py
                ports:
                  - name: http
                    containerPort: 5000
                resources:
                  limits:
                    cpu: "1"
                    memory: 1Gi
                  requests:
                    cpu: "1"
                    memory: 1Gi