apiVersion: system.hero.ai/v1alpha1
kind: Application
metadata:
  #  annotations:
  #    application.leinao.ai/action: stop   # start 、updategit
  name: llm-predict
  namespace: llm-example
spec:
  version: v1
#  serviceMesh:
#    backend: istio
#    ingressGateway: false
#    subRoute:
#      weight: 100  # 权重，多版本的情况下可指定流量分发比例
#      match:  # 屁屁条件
#        - uri: #  url支持 前缀（prefix） 、 完全匹配（exact）、 正则匹配（regex）
#            prefix: /v1alpha1
#          headers: # 认证信息或者其他匹配规则，匹配请求头携带以下内容
#            token:
#              exact: "llm-predict"
#      retries:
#        autoRetire: true
#        attempts: 3 # 重试次数
#        perTryTimeout: 10s  # 每次重试的超时时间
#      trafficPolicy:
#        loadBalancer: 4 # 0: default 2 随机策略 4:轮训策略
#        circuitBreaker:
#          maxConnections: 100 # 每个连接的最大请求数
#          http1MaxPendingRequests: 100  # 到目的地的最大待处理HTTP/1.1请求数
#          consecutiveErrors: 5  # 连续多少个误则触发熔断
#          interval: 1s # 统计错误数的时间窗口
#          baseEjectionTime: 5m # 熔断后多久自动恢复
#          maxEjectionPercent: 100 # 最大熔断比例
  autoScaling:
    backend: keda
    minReplicas: 1
    maxReplicas: 10
    triggers:
      - type: cpu
        metadata:
          type: Utilization
          value: "50"
      - type: memory
        metadata:
          type: AverageValue
          value: "512"  #单位byte
      - type: prometheus
        metadata:
          metricName: http_requests_total
          threshold: '20'
      - type: prometheus
        metadata:
          metricName: http_requests_total
          threshold: '20'
    serverless:
      - type: prometheus
        metadata:
          metricName: http_requests_total
          threshold: '20'
  scheduler:
    schedulerName: volcano
    minAvailable: 2
  gateway:
    backend: istio
    host: llm-predict # 同app名
  servers:
#    image-describe:
#      - version: v1
#        replicas: 1
#        template:
#          metadata:
#            labels:
#              app: image-describe
#          spec:
#            containers:
#              - name: image-describe
#                image: registry.cn-hangzhou.aliyuncs.com/ruanxingbaozi/pytorch:image_recognition_demo
#                imagePullPolicy: Always
#                args:
#                  - bash
#                  - -c
#                  - python image_recognition_server.py
#                ports:
#                  - name: http
#                    containerPort: 5000
#                env:
#                  - name: DEMO_GREETING
#                    value: "Hello from the environment"
#                resources:
#                  limits:
#                    cpu: "1"
#                    memory: 4Gi
#                    #nvidia.com/nvidia-rtx-3090: "1"
#                  requests:
#                    cpu: "1"
#                    memory: 4Gi
#                    #nvidia.com/nvidia-rtx-3090: "1"
#        serviceMesh:
#          backend: istio
#          ingressGateway: true
#          subRoute:
#            weight: 100
#            match:
#              - headers:
#                  end-user:
#                    exact: application
#                uri:
#                  prefix: /application/v1
#            retries:
#              autoRetire: true
#              attempts: 3 # 重试次数
#        autoScaling:
#          backend: keda
#          minReplicas: 1
#          maxReplicas: 10
#          triggers:
#            - type: cpu
#              metadata:
#                type: Utilization
#                value: "50"
#            - type: memory
#              metadata:
#                type: AverageValue
#                value: "1024"
##            - type: prometheus
##              metadata:
##                metricName: http_requests_total
##                threshold: '20'
#          serverless:
#            - type: memory
#              metadata:
#                type: Utilization
#                value: "1024"
#    image-predict:
#      - version: v1
#        replicas: 1
#        serviceMesh:
#          subRoute:
#            weight: 80
#        template:
#          metadata:
#            labels:
#              app: image-predict
#          spec:
#            containers:
#              - name: image-predict
#                image: registry.cn-hangzhou.aliyuncs.com/ruanxingbaozi/pytorch:image_recognition_demo
#                imagePullPolicy: Always
#                args:
#                  - bash
#                  - -c
#                  - python image_classification_server.py
#                ports:
#                  - name: http
#                    containerPort: 5000
#                resources:
#                  limits:
#                    cpu: "1"
#                    memory: 1Gi
#                    #nvidia.com/nvidia-rtx-3090: "1"
#                  requests:
#                    cpu: "1"
#                    memory: 1Gi
#                    #nvidia.com/nvidia-rtx-3090: "1"
#        autoScaling:
#          backend: keda
#          minReplicas: 1
#          maxReplicas: 10
#          triggers:
#            - type: cpu
#              metadata:
#                type: Utilization
#                value: "50"
#            - type: memory
#              metadata:
#                type: AverageValue
#                value: "1024"
##            - type: prometheus
##              metadata:
##                metricName: http_requests_total
##                threshold: '20'
#          serverless:
#            - type: memory
#              metadata:
#                type: Utilization
#                value: "1024"
#      - version: v2
#        replicas: 1
#        serviceMesh:
#          subRoute:
#            weight: 20
#        template:
#          metadata:
#            labels:
#              app: image-predict
#          spec:
#            containers:
#              - name: image-predict
#                image: registry.cn-hangzhou.aliyuncs.com/ruanxingbaozi/pytorch:image_recognition_demo
#                imagePullPolicy: Always
#                args:
#                  - bash
#                  - -c
#                  - python image_classification_server.py
#                ports:
#                  - name: http
#                    containerPort: 5000
#                resources:
#                  limits:
#                    cpu: "1"
#                    memory: 1Gi
#                    #nvidia.com/nvidia-rtx-3090: "1"
#                  requests:
#                    cpu: "1"
#                    memory: 1Gi
#                    #nvidia.com/nvidia-rtx-3090: "1"
    llm-predict:
      - version: v1
        replicas: 1
        serviceMesh:
          ingressGateway: true
          backend: istio
          subRoute:
            weight: 20
        template:
          metadata:
            labels:
              app: llm-predict
          spec:
            containers:
              - name: llm-predict
                image: registry.cn-hangzhou.aliyuncs.com/ruanxingbaozi/pytorch:image_recognition_demo
                imagePullPolicy: Always
                args:
                  - bash
                  - -c
                  - python llm_server.py
                ports:
                  - name: http
                    containerPort: 5000
                resources:
                  limits:
                    cpu: "1"
                    memory: 1Gi
                    #nvidia.com/nvidia-rtx-3090: "1"
                  requests:
                    cpu: "1"
                    memory: 1Gi
                    #nvidia.com/nvidia-rtx-3090: "1"
#    llm-inference-app:
#      - version: v1
#        replicas: 1
#        serviceMesh:
#          backend: istio
#          ingressGateway: true
#          subRoute:
#            weight: 100
#            match:
#              - uri:
#                  prefix: /
#            retries:
#              autoRetire: true
#              attempts: 3 # 重试次数
#        template:
#          metadata:
#            labels:
#              app: llm-inference-app
#          spec:
#            containers:
#              - name: llm-inference-app
#                image: registry.cn-hangzhou.aliyuncs.com/ruanxingbaozi/pytorch:image_recognition_demo
#                imagePullPolicy: Always
#                args:
#                  - bash
#                  - -c
#                  - python app.py
#                ports:
#                  - name: http
#                    containerPort: 7860
#                resources:
#                  limits:
#                    cpu: "1"
#                    memory: 1Gi
#                    #nvidia.com/nvidia-rtx-3090: "1"
#                  requests:
#                    cpu: "1"
#                    memory: 1Gi
#                    #nvidia.com/nvidia-rtx-3090: "1"
status:
  availableReplicas: 5
  availableServers: 4
  phase: Running
  replicas: 5
  serverStates:
    image-describe:
      availableReplicas: 1
      replicas: 1
    image-predict:
      availableReplicas: 2
      replicas: 2
    llm-inference-app:
      availableReplicas: 1
      replicas: 1
    llm-predict:
      availableReplicas: 1
      replicas: 1
