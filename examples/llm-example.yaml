#apiVersion: v1
#kind: Namespace
#metadata:
#  labels:
#    istio-injection: enabled
#  name: llm-example
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: image-describe-v1
  namespace: llm-example
  labels:
    app: image-describe
    version: v1
spec:
  replicas: 1
  selector:
    matchLabels:
      app: image-describe
      version: v1
  template:
    metadata:
      labels:
        app: image-describe
        version: v1
    spec:
      serviceAccountName: llm-inference-app
      containers:
        - image: registry.cn-hangzhou.aliyuncs.com/ruanxingbaozi/pytorch:image_recognition_demo
          imagePullPolicy: Always
          name: web-svc
          args:
            - bash
            - -c
            - python image_recognition_server.py
          ports:
            - containerPort: 5000
              name: http
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: image-predict-v1
  namespace: llm-example
  labels:
    app: image-predict
    version: v1
spec:
  replicas: 1
  selector:
    matchLabels:
      app: image-predict
      version: v1
  template:
    metadata:
      labels:
        app: image-predict
        version: v1
    spec:
      serviceAccountName: llm-inference-app
      containers:
        - image: registry.cn-hangzhou.aliyuncs.com/ruanxingbaozi/pytorch:image_recognition_demo
          imagePullPolicy: Always
          name: web-svc
          args:
            - bash
            - -c
            - python image_classification_server.py
          ports:
            - containerPort: 5000
              name: http
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: llm-predict-v1
  namespace: llm-example
  labels:
    app: llm-predict
    version: v1
spec:
  replicas: 1
  selector:
    matchLabels:
      app: llm-predict
      version: v1
  template:
    metadata:
      labels:
        app: llm-predict
        version: v1
    spec:
      serviceAccountName: llm-inference-app
      containers:
        - image: registry.cn-hangzhou.aliyuncs.com/ruanxingbaozi/pytorch:image_recognition_demo
          imagePullPolicy: Always
          name: web-svc
          args:
            - bash
            - -c
            - python llm_server.py
          ports:
            - containerPort: 5000
              name: http
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: llm-predict-v2
  namespace: llm-example
  labels:
    app: llm-predict
    version: v2
spec:
  replicas: 1
  selector:
    matchLabels:
      app: llm-predict
      version: v2
  template:
    metadata:
      labels:
        app: llm-predict
        version: v2
    spec:
      serviceAccountName: llm-inference-app
      containers:
        - image: registry.cn-hangzhou.aliyuncs.com/ruanxingbaozi/pytorch:image_recognition_demo
          imagePullPolicy: Always
          name: web-svc
          args:
            - bash
            - -c
            - python llm_server.py
          ports:
            - containerPort: 5000
              name: http
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: llm-inference-app-v1
  namespace: llm-example
  labels:
    app: llm-inference-app
    version: v1
spec:
  replicas: 1
  selector:
    matchLabels:
      app: llm-inference-app
      version: v1
  template:
    metadata:
      labels:
        app: llm-inference-app
        version: v1
    spec:
      serviceAccountName: llm-inference-app
      containers:
        - image: registry.cn-hangzhou.aliyuncs.com/ruanxingbaozi/pytorch:image_recognition_demo
          imagePullPolicy: Always
          name: web-svc
          args:
            - bash
            - -c
            - python app.py
          ports:
            - containerPort: 7860
              name: http
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: llm-inference-app
  namespace: llm-example
---
apiVersion: v1
kind: Service
metadata:
  name: image-describe
  namespace: llm-example
spec:
  ports:
    - name: http
      port: 5000
      targetPort: 5000
  selector:
    app: image-describe
---
apiVersion: v1
kind: Service
metadata:
  name: image-predict
  namespace: llm-example
spec:
  ports:
    - name: http
      port: 5000
      targetPort: 5000
  selector:
    app: image-predict
---
apiVersion: v1
kind: Service
metadata:
  name: llm-predict
  namespace: llm-example
spec:
  ports:
    - name: http
      port: 5000
      targetPort: 5000
  selector:
    app: llm-predict
---
apiVersion: v1
kind: Service
metadata:
  name: llm-inference-app
  namespace: llm-example
spec:
  ports:
    - name: http
      port: 7860
      targetPort: 7860
  selector:
    app: llm-inference-app