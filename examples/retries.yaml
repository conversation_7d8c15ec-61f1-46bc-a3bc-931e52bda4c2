apiVersion: system.hero.ai/v1alpha1
kind: Application
metadata:
  name: retries
spec:
  version: v1
  scheduler:
    schedulerName: default
  serviceMesh:
    backend: istio
    subRoute:
      retries:
        autoRetire: true
        attempts: 3 # 重试次数
        perTryTimeout: 1s  # 每次重试的超时时间
  servers:
    httpbin:
      - version: v1
        replicas: 1
        serviceMesh:
          subRoute:
            weight: 100
        template:
          metadata:
            labels:
              app: httpbin
          spec:
            serviceAccountName: httpbin
            containers:
              - image: docker.io/kong/httpbin
                imagePullPolicy: IfNotPresent
                name: httpbin
                ports:
                  - containerPort: 80
    sleep:
      - version: v1
        replicas: 1
        serviceMesh:
          subRoute:
            weight: 100
        template:
          metadata:
            labels:
              app: sleep
          spec:
            terminationGracePeriodSeconds: 0
            serviceAccountName: sleep
            containers:
              - name: sleep
                image: curlimages/curl
                command: [ "/bin/sleep", "infinity" ]
                imagePullPolicy: IfNotPresent


# 重试前提： 服务不可用、连接失败、超时或服务响应错误（如HTTP 5xx错误）的情况。
# 重试时间指：等待每次重试响应的最大时间

#  export SLEEP_POD=$(kubectl get pods -l app=sleep -o 'jsonpath={.items[0].metadata.name}')
#  kubectl exec "$SLEEP_POD" -c sleep --  curl -quiet http://httpbin:80/status/503
#
#  查看日志
#  export HTTPBIN_POD=$(kubectl get pods -l app=httpbin -o 'jsonpath={.items[0].metadata.name}')
#  kubectl logs "$HTTPBIN_POD" istio-proxy --tail 200 -f
#  看到如下日志说明没问题，一共四次请求，第一次失败和重试三次
#  [2023-12-13T09:45:15.438Z] [1] - 503 via_upstream - "-" 0 0 29 "-" "f47bcd2c-2484-9f9b-8cae-b9173a8fe9d8" inbound|80|| 127.0.0.6:41335 100.67.10.84:80 100.67.10.103:33796 default
#  [2023-12-13T09:45:15.475Z] [2] - 503 via_upstream - "-" 0 0 0 "-" "f47bcd2c-2484-9f9b-8cae-b9173a8fe9d8" inbound|80|| 127.0.0.6:41335 100.67.10.84:80 100.67.10.103:33796 default
#  [2023-12-13T09:45:15.495Z] [3] - 503 via_upstream - "-" 0 0 0 "-" "f47bcd2c-2484-9f9b-8cae-b9173a8fe9d8" inbound|80|| 127.0.0.6:41335 100.67.10.84:80 100.67.10.103:33796 default
#  [2023-12-13T09:45:15.559Z] [4] - 503 via_upstream - "-" 0 0 1 "-" "f47bcd2c-2484-9f9b-8cae-b9173a8fe9d8" inbound|80|| 127.0.0.6:41335 100.67.10.84:80 100.67.10.103:33796 default