apiVersion: system.hero.ai/v1alpha1
kind: Application
metadata:
  name: mnist-tf-inference
  namespace: mnist-demo
spec:
  version: v1
  scheduler:
    schedulerName: default
  gateway:
    backend: istio
    host: mnist-tf-inference # 同app名
  servers:
    llm-inference-app:
      - version: v1
        replicas: 1
        serviceMesh:
          backend: istio
          ingressGateway: true
          subRoute:
            weight: 100
            match:
              - uri:
                  prefix: /
            retries:
              autoRetire: true
              attempts: 3
        template:
          metadata:
            labels:
              app: llm-inference-app
          spec:
            containers:
              - name: mnist-tf-inference
                image: registry.bitahub.com:5000/xrwang-mnist-flask-serving:tf-2.4.3-gpu
                imagePullPolicy: Always
                args:
                  - python /code/dense-mnist-tf/serving/serving.py
                ports:
                  - name: http
                    containerPort: 5000
                resources:
                  limits:
                    cpu: "2"
                    memory: 2Gi
                  requests:
                    cpu: "2"
                    memory: 2Gi
