apiVersion: system.hero.ai/v1alpha1
kind: Application
metadata:
  annotations:
    job.id: a9787820239351808229120111

    application.leinao.ai/action: restart     
  name: xrwang-test
  namespace: xrwang-test
spec:
  autoScaling:
    backend: keda
  # gateway:
  #   backend: istio
  #   host: a9787820238827520969052.***********.nip.io
  scheduler:
    schedulerName: default
  servers:
    cuiaabb:
    - replicas: 1
      scheduler:
        schedulerName: default
      template:
        metadata:
          labels:
            app: cuiaabb
        spec:
          containers:
          - args:
            - python  llm_server.py
            image: registry.cnbita.com:5000/user-images/systemuser-inference:inference
            imagePullPolicy: Always
            name: xrwang
            ports:
            - containerPort: 5000
              name: http
              protocol: TCP
            # resources:
            #   limits:
            #     cpu: "1"
            #     memory: 1Gi
            #   requests:
            #     cpu: "1"
            #     memory: 1Gi
            volumeMounts:
            - mountPath: /code
              name: mount-code
          initContainers:
          - args:
            - sh
            - -c
            - set -e;git clone https://gitlab.bitahub.com/cdd/ceni.git -b dev;ls -l
            image: registry.cnbita.com:5000/leinaoyun/git:2.34.5
            imagePullPolicy: IfNotPresent
            name: code-init
            resources: {}
            workingDir: /code
            volumeMounts:
            - mountPath: /code
              name: mount-code
          volumes:
          - emptyDir: {}
            name: mount-code
      version: a9787436549664768308371

