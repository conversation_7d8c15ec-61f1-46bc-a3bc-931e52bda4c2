apiVersion: system.hero.ai/v1alpha1
kind: Application
metadata:
  name: llm-predict-v2
  namespace: heros-user
spec:
  version: v1
  scheduler:
    schedulerName: default
  gateway:
    backend: istio
    host: llm-predict # 同app名
  servers:
    llm-inference-app:
      - version: v1
        replicas: 1
        serviceMesh:
          backend: istio
          ingressGateway: true
          subRoute:
            weight: 100
            match:
              - uri:
                  prefix: /
            retries:
              autoRetire: true
              attempts: 3
        template:
          metadata:
            labels:
              app: llm-inference-app
              # 挂载统一存储创建的pvc需要设置的标签
              # serverless.fluid.io/inject: "true"
              fuse.serverful.fluid.io/inject: "true"
          spec:
            containers:
              - name: llm-inference-app
                image: registry.cn-hangzhou.aliyuncs.com/ruanxingbaozi/pytorch:image_recognition_demo
                imagePullPolicy: Always
                args:
                  - bash
                  - -c
                  - python app.py
                ports:
                  - name: http
                    containerPort: 7860
                resources:
                  limits:
                    cpu: "1"
                    memory: 1Gi
                  requests:
                    cpu: "1"
                    memory: 1Gi
                volumeMounts:
                - name: lgy
                  mountPath: /data/b20231121090340616awdp4
                  subPath: /data/b20231121090340616awdp4
                - name: rekk
                  mountPath: /data/b20231121090340616a
                  subPath: /data/tm
                  
                # - name: test                  
                # - name: test
                #   mountPath: /data/b20231121104836446uso8fy
            # 暂时不支持挂载多个桶    
            volumes:
              - name: lgy
                persistentVolumeClaim:
                  claimName: b20231121104836446uso8fy
              # - name: test
              #   persistentVolumeClaim:
              #     claimName: b20231121090340616awdp4e