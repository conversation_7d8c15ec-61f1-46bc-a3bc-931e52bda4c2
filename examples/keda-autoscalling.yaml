apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: image-describe
  namespace: llm-example
  labels:
    run: image-describe
spec:
  advanced:
    horizontalPodAutoscalerConfig:
      behavior:
        scaleDown:
          stabilizationWindowSeconds: 0
  maxReplicaCount: 2
  minReplicaCount: 1
  scaleTargetRef:
    name: image-describe
  triggers:
    - type: cpu
      metadata:
        type: Utilization
        value: "50"

---

apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: memory-describe
  namespace: llm-example
  labels:
    run: test-mem
spec:
  advanced:
    horizontalPodAutoscalerConfig:
      behavior:
        scaleDown:
          stabilizationWindowSeconds: 1
  pollingInterval: 1
  scaleTargetRef:
    name: test-mem
  minReplicaCount: 1
  maxReplicaCount: 3
  cooldownPeriod: 1
  triggers:
    - type: memory
      metadata:
        type: Utilization
        value: "1024"
    - type: kubernetes-workload
      metadata:
        podSelector: 'pod=test-mem'
        value: '1'

---
kind: ScaledObject
metadata:
  name: prometheus-describe
  namespace: llm-example
spec:
  scaleTargetRef:
    name: {{.DeploymentName}}
  minReplicaCount: 1
  maxReplicaCount: 4
  pollingInterval: 3
  cooldownPeriod:  1
  triggers:
    - type: prometheus
      metadata:
        serverAddress: http://{{.PrometheusServerName}}.{{.TestNamespace}}.svc
        metricName: http_requests_total
        threshold: '20'
        activationThreshold: '20'
        query: sum(rate(http_requests_total{app="MonitoredAppName"}[2m]))
---
#指定的开始时间到结束时间，达到期望的副本，即在指定的时间范围内，KEDA ScaledObject 将自动执行缩放操作，以使应用程序的副本数达到 desiredReplicas 中指定的值
apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: {{.ScaledObjectName}}
  namespace: {{.TestNamespace}}
spec:
  scaleTargetRef:
    name: {{.DeploymentName}}
  pollingInterval: 5
  cooldownPeriod: 5
  minReplicaCount: 1
  maxReplicaCount: 10
  advanced:
    horizontalPodAutoscalerConfig:
      behavior:
        scaleDown:
          stabilizationWindowSeconds: 15
  triggers:
    - type: cron
      metadata:
        timezone: Etc/UTC
        start: {{.StartMin}} * * * *
        end: {{.EndMin}} * * * *
        desiredReplicas: '4'
