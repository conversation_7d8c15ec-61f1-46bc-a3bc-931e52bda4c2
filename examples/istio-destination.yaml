apiVersion: networking.istio.io/v1alpha3
kind: DestinationRule
metadata:
  name: image-describe
  namespace: llm-example
spec:
  host: image-describe
  subsets:
    - name: v1
      labels:
        version: v1
---
apiVersion: networking.istio.io/v1alpha3
kind: DestinationRule
metadata:
  name: image-predict
  namespace: llm-example
spec:
  host: image-predict
  subsets:
    - name: v1
      labels:
        version: v1
---
apiVersion: networking.istio.io/v1alpha3
kind: DestinationRule
metadata:
  name: llm-predict
  namespace: llm-example
spec:
  host: llm-predict
  subsets:
    - name: v1
      labels:
        version: v1
    - name: v2
      labels:
        version: v2
---
apiVersion: networking.istio.io/v1alpha3
kind: DestinationRule
metadata:
  name: llm-inference-app
  namespace: llm-example
spec:
  host: llm-inference-app
  trafficPolicy:
    tls:
      mode: ISTIO_MUTUAL
    loadBalancer:
      simple: ROUND_ROBIN
    connectionPool:
      tcp:
        maxConnections: 100
        connectTimeout: 500ms
#        tcpkeepalive:
#          probes: 5
#          time: 3600s
#          interval: 75s
      http:
        http1MaxPendingRequests: 100
        maxRequestsPerConnection: 1
  subsets:
    - name: v1
      labels:
        version: v1