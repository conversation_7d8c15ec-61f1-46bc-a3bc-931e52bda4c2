apiVersion: system.hero.ai/v1alpha1
kind: Application
metadata:
  name: trafficpolicy
spec:
  version: v1
  scheduler:
    schedulerName: default
  serviceMesh:
    backend: istio
    subRoute:
      trafficPolicy:
        circuitBreaker:
          maxConnections: 1 # 每个连接的最大请求数
          http1MaxPendingRequests: 1  # 到目的地的最大待处理HTTP/1.1请求数
          consecutiveErrors: 1  # 连续多少个误则触发熔断
          interval: 1s # 统计错误数的时间窗口
          baseEjectionTime: 3m # 熔断后多久自动恢复
          maxEjectionPercent: 100 # 最大熔断比例
  gateway:
    backend: istio
    host: trafficPolicy # 同app名
  servers:
    httpbin:
      - version: v1
        replicas: 1
        serviceMesh:
          ingressGateway: true
          subRoute:
            weight: 100
        template:
          metadata:
            labels:
              app: httpbin
          spec:
            serviceAccountName: httpbin
            containers:
              - image: docker.io/kong/httpbin
                imagePullPolicy: IfNotPresent
                name: httpbin
                ports:
                  - containerPort: 80
    fortio:
      - version: v1
        replicas: 1
        serviceMesh:
          subRoute:
            weight: 100
        template:
          metadata:
            annotations:
              proxy.istio.io/config: |-
                proxyStatsMatcher:
                  inclusionPrefixes:
                  - "cluster.outbound"
                  - "cluster_manager"
                  - "listener_manager"
                  - "server"
                  - "cluster.xds-grpc"
            labels:
              app: fortio
          spec:
            containers:
              - name: fortio
                image: fortio/fortio:latest_release
                imagePullPolicy: Always
                ports:
                  - containerPort: 8080
                    name: http-fortio
                  - containerPort: 8079
                    name: grpc-ping



# 测试点： 连接池超额和熔断后的拒绝访问
#  export FORTIO_POD=$(kubectl get pods -l app=fortio -o 'jsonpath={.items[0].metadata.name}')
#  kubectl exec "$FORTIO_POD" -c fortio -- /usr/bin/fortio curl -quiet http://httpbin:80/get
#  连接池测试：
#  kubectl exec "$FORTIO_POD" -c fortio -- /usr/bin/fortio load -c 10 -qps 0 -n 30 -loglevel Warning http://httpbin:80/get
#
#  熔断测试：
#  kubectl exec "$FORTIO_POD" -c fortio -- /usr/bin/fortio curl -quiet http://httpbin:80/status/503
#  kubectl exec "$FORTIO_POD" -c fortio -- /usr/bin/fortio curl -quiet http://httpbin:80/get

#
#2023-12-12T08:51:30.128002Z	warning	envoy config external/envoy/source/extensions/config_subscription/grpc/grpc_stream.h:152	StreamAggregatedResources gRPC config stream to xds-grpc closed: 16, authentication failure	thread=14
#2023-12-12T08:51:33.195399Z	info	xdsproxy	connected to upstream XDS server: istiod.istio-system.svc:15012
#2023-12-12T08:51:33.201488Z	warn	xdsproxy	upstream [144] terminated with unexpected error rpc error: code = Unauthenticated desc = authentication failure
#2023-12-12T08:51:33.201967Z	warning	envoy config external/envoy/source/extensions/config_subscription/grpc/grpc_stream.h:152	StreamAggregatedResources gRPC config stream to xds-grpc closed: 16, authentication failure	thread=14
#2023-12-12T08:51:36.210895Z	error	failed scraping envoy metrics: error scraping http://localhost:15090/stats/prometheus: Get "http://localhost:15090/stats/prometheus": dial tcp 127.0.0.1:15090: connect: connection refused
#2023-12-12T08:51:51.210334Z	error	failed scraping envoy metrics: error scraping http://localhost:15090/stats/prometheus: Get "http://localhost:15090/stats/prometheus": dial tcp 127.0.0.1:15090: connect: connection refused
#2023-12-12T08:51:54.644349Z	info	xdsproxy	connected to upstream XDS server: istiod.istio-system.svc:15012
#2023-12-12T08:51:54.648262Z	warn	xdsproxy	upstream [145] terminated with unexpected error rpc error: code = Unauthenticated desc = authentication failure
#2023-12-12T08:51:54.648927Z	warning	envoy config external/envoy/source/extensions/config_subscription/grpc/grpc_stream.h:152	StreamAggregatedResources gRPC config stream to xds-grpc closed: 16, authentication failure	thread=14
#2023-12-12T08:52:06.211023Z	error	failed scraping envoy metrics: error scraping http://localhost:15090/stats/prometheus: Get "http://localhost:15090/stats/prometheus": dial tcp 127.0.0.1:15090: connect: connection refused
#2023-12-12T08:52:11.620616Z	info	xdsproxy	connected to upstream XDS server: istiod.istio-system.svc:15012
#2023-12-12T08:52:11.624915Z	warn	xdsproxy	upstream [146] termina




#accessLogFormat: "[%START_TIME%] [%REQ(X-ENVOY-ATTEMPT-COUNT)%] %REQ(X-META-PROTOCOL-APPLICATION-PROTOCOL)%
#     %RESPONSE_CODE% %RESPONSE_CODE_DETAILS% %CONNECTION_TERMINATION_DETAILS% \"%UPSTREAM_TRANSPORT_FAILURE_REASON%\"
#     %BYTES_RECEIVED% %BYTES_SENT% %DURATION% \"%REQ(X-FORWARDED-FOR)%\" \"%REQ(X-REQUEST-ID)%\" %UPSTREAM_CLUSTER%
#     %UPSTREAM_LOCAL_ADDRESS% %DOWNSTREAM_LOCAL_ADDRESS% %DOWNSTREAM_REMOTE_ADDRESS% %ROUTE_NAME%\n"


