apiVersion: system.hero.ai/v1alpha1
kind: Application
metadata:
  name: text-imagetotext-01
  namespace: llm-example
spec:
  version: v1
  deploymentMode: serverless
  autoScaling:
    maxReplicas: 10
  scheduler:
    schedulerName: volcano
    queue: default
  servers:
    llm-inference-app:
      - serverName: text-imagetotext
        modelSource: model-repository
        version: v1
        template:
          metadata:
            labels:
              app: text-imagetotext-01
          spec:
            containers:
              - name: text-imagetotext
                image: registry.cnbita.com:5000/jingxiang/transformers-apis:v1
                imagePullPolicy: Always
                ports:
                  - name: http1    #Name must be empty, or one of: 'h2c', 'http1'
                    containerPort: 5000
                env:
                  - name: HF_ENDPOINT
                    value: https://www.bitahub.com/model-repository
                  - name: TOKEN
                    value:
                  - name: MODEL_ID
                    value: ailab/vit-gpt2-image-captioning
                  - name: MODEL_TASK_TYPE
                    value: image-to-text
                resources:
                  limits:
                    cpu: 2
                    memory: 8Gi
                  requests:
                    cpu: 2
                    memory: 8Gi