apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: image-describe
  namespace: llm-example
spec:
  hosts:
    - image-describe
  http:
    - route:
      - destination:
          host: image-describe
          subset: v1
---
apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: image-predict
  namespace: llm-example
spec:
  hosts:
    - image-predict
  http:
    - route:
      - destination:
          host: image-predict
          subset: v1
---
apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: llm-predict
  namespace: llm-example
spec:
  hosts:
    - llm-predict
  http:
    - route:
      - destination:
          host: llm-predict
          subset: v1
        weight: 80
      - destination:
          host: llm-predict
          subset: v2
        weight: 20
---
apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: llm-inference-app
  namespace: llm-example
spec:
  hosts:
    - "*"
  gateways:
    - llm-inference-app-gateway
  http:
    - match:
        - uri:
            prefix: /
      route:
        - destination:
            host: llm-inference-app
            port:
              number: 7860
---