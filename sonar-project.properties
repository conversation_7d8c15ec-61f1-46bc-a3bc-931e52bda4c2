sonar.sourceEncoding=UTF-8
sonar.sources=.
sonar.language=go
sonar.inclusions=**/pkg/**,**/sonar/**
sonar.exclusions=**/pkg/auth_manager/**,**/pkg/informers/**,**/pkg/simple/**,**/pkg/utils/**
sonar.tests=.
sonar.test.inclusions=**/*_test.go
sonar.test.exclusions=**/vendor/**
sonar.go.tests.reportPaths=sonar/reports/test-report.out
sonar.go.coverage.reportPaths=sonar/reports/coverage.out
sonar.go.golangci-lint.reportPaths=sonar/reports/report.xml
sonar.qualitygate.wait=true