package app

import (
	"context"
	"fmt"
	"os"

	httpv1alpha1 "github.com/kedacore/http-add-on/operator/apis/http/v1alpha1"
	keda "github.com/kedacore/keda/v2/apis/keda/v1alpha1"
	"github.com/spf13/cobra"
	systemv1alpha1 "gitlab.leinao.ai/application-controller/apis/application/v1alpha1"
	"gitlab.leinao.ai/application-controller/cmd/controller-manager/app/options"
	"gitlab.leinao.ai/application-controller/pkg/simple/client/k8s"
	clientnetworking "istio.io/client-go/pkg/apis/networking/v1alpha3"
	"k8s.io/apimachinery/pkg/runtime"
	utilerrors "k8s.io/apimachinery/pkg/util/errors"
	utilruntime "k8s.io/apimachinery/pkg/util/runtime"
	clientgoscheme "k8s.io/client-go/kubernetes/scheme"
	cliflag "k8s.io/component-base/cli/flag"
	"k8s.io/klog/v2"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/healthz"
	"sigs.k8s.io/controller-runtime/pkg/manager"
	metricsserver "sigs.k8s.io/controller-runtime/pkg/metrics/server"
	batch "volcano.sh/apis/pkg/apis/batch/v1alpha1"
)

const version = "v1"

var (
	scheme   = runtime.NewScheme()
	setupLog = ctrl.Log.WithName("controller-manager")
)

func init() {
	utilruntime.Must(clientgoscheme.AddToScheme(scheme))
	utilruntime.Must(clientnetworking.AddToScheme(scheme))
	utilruntime.Must(systemv1alpha1.AddToScheme(scheme))
	utilruntime.Must(keda.AddToScheme(scheme))
	utilruntime.Must(httpv1alpha1.AddToScheme(scheme))
	utilruntime.Must(batch.AddToScheme(scheme))
	//+kubebuilder:scaffold:scheme
}

func NewControllerManagerCommand() *cobra.Command {
	s := options.NewControllerManagerOptions()
	conf, err := options.TryLoadFromDisk()
	if err == nil {
		// make sure LeaderElection is not nil
		s = &options.ControllerManagerOptions{
			KubernetesOptions: conf.KubernetesOptions,
			LeaderElection:    s.LeaderElection,
			LeaderElect:       s.LeaderElect,
			Namespace:         s.Namespace,
		}
	} else {
		klog.Fatal("Failed to load configuration from disk", err)
	}

	cmd := &cobra.Command{
		Use:  "app-server",
		Long: `app-server controller manager is a daemon that`,
		Run: func(cmd *cobra.Command, args []string) {
			if errs := s.Validate(allControllers); len(errs) != 0 {
				klog.Error(utilerrors.NewAggregate(errs))
				os.Exit(1)
			}

			if err = run(ctrl.SetupSignalHandler(), s); err != nil {
				klog.Error(err)
				os.Exit(1)
			}
		},
		SilenceUsage: true,
	}

	fs := cmd.Flags()
	namedFlagSets := s.Flags(allControllers)
	for _, f := range namedFlagSets.FlagSets {
		fs.AddFlagSet(f)
	}
	usageFmt := "Usage:\n  %s\n"
	cmd.SetHelpFunc(func(cmd *cobra.Command, args []string) {
		_, _ = fmt.Fprintf(cmd.OutOrStdout(), "%s\n\n"+usageFmt, cmd.Long, cmd.UseLine())
		cliflag.PrintSections(cmd.OutOrStdout(), namedFlagSets, 1)
	})
	versionCmd := &cobra.Command{
		Use:   "version",
		Short: "Print the version of Container controller",
		Run: func(cmd *cobra.Command, args []string) {
			cmd.Println(version)
		},
	}

	cmd.AddCommand(versionCmd)
	return cmd
}

func run(ctx context.Context, s *options.ControllerManagerOptions) error {
	kubernetesClient, err := k8s.NewKubernetesClient(s.KubernetesOptions)
	if err != nil {
		klog.Errorf("Failed to create kubernetes clientset %v", err)
		return err
	}

	mgrOptions := manager.Options{
		Metrics:                metricsserver.Options{BindAddress: ":8080"},
		HealthProbeBindAddress: ":8081",
		Scheme:                 scheme,
	}

	if s.LeaderElect {
		mgrOptions = manager.Options{
			Metrics:                 metricsserver.Options{BindAddress: ":8080"},
			LeaderElection:          s.LeaderElect,
			LeaderElectionNamespace: "heros-system",
			LeaderElectionID:        "controller-manager-leader-election",
			LeaseDuration:           &s.LeaderElection.LeaseDuration,
			RetryPeriod:             &s.LeaderElection.RetryPeriod,
			RenewDeadline:           &s.LeaderElection.RenewDeadline,
			HealthProbeBindAddress:  ":8081",
			Scheme:                  scheme,
		}
	}
	klog.V(0).Info("setting up manager")
	ctrl.SetLogger(klog.NewKlogr())

	mgr, err := manager.New(kubernetesClient.Config(), mgrOptions)
	if err != nil {
		setupLog.Error(err, "unable to start manager")
		os.Exit(1)
	}

	if err = addAllControllers(mgr, s); err != nil {
		klog.Fatalf("unable to register controllers to the manager: %v", err)
	}

	//if os.Getenv("ENABLE_WEBHOOKS") != "false" {
	//	if err = (&systemv1alpha1.Application{}).SetupWebhookWithManager(mgr); err != nil {
	//		setupLog.Error(err, "unable to create webhook", "webhook", "Application")
	//		os.Exit(1)
	//	}
	//}
	//+kubebuilder:scaffold:builder

	if err := mgr.AddHealthzCheck("healthz", healthz.Ping); err != nil {
		setupLog.Error(err, "unable to set up health check")
		os.Exit(1)
	}
	if err := mgr.AddReadyzCheck("readyz", healthz.Ping); err != nil {
		setupLog.Error(err, "unable to set up ready check")
		os.Exit(1)
	}

	setupLog.Info("starting manager")
	if err := mgr.Start(ctx); err != nil {
		setupLog.Error(err, "problem running manager")
		os.Exit(1)
	}

	return nil
}
