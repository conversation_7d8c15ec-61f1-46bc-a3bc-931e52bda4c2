package options

import (
	"fmt"

	"github.com/spf13/viper"
	"gitlab.leinao.ai/application-controller/pkg/simple/client/k8s"
)

const (
	// DefaultConfigurationName is the default name of configuration
	defaultConfigurationName = "controller-manager"

	// DefaultConfigurationPath the default location of the configuration file
	defaultConfigurationPath = "/etc/controller-manager"
)

type Config struct {
	KubernetesOptions *k8s.KubernetesOptions `json:"kubernetes,omitempty" yaml:"kubernetes,omitempty" mapstructure:"kubernetes"`
	Namespace         string                 `json:"namespace,omitempty" yaml:"namespace"`
}

func TryLoadFromDisk() (*Config, error) {
	viper.SetConfigName(defaultConfigurationName)
	viper.AddConfigPath(defaultConfigurationPath)

	// Load from current working directory, only used for debugging
	viper.AddConfigPath(".")

	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); ok {
			return nil, err
		}
		return nil, fmt.Errorf("error parsing configuration file %s", err)

	}

	conf := New()
	if err := viper.Unmarshal(conf); err != nil {
		return nil, err
	}
	return conf, nil
}

// newConfig creates a default non-empty Config
func New() *Config {
	return &Config{
		KubernetesOptions: k8s.NewKubernetesOptions(),
	}
}
