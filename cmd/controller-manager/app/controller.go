package app

import (
	"fmt"

	"gitlab.leinao.ai/application-controller/cmd/controller-manager/app/options"
	"gitlab.leinao.ai/application-controller/pkg/config"
	"gitlab.leinao.ai/application-controller/pkg/controller"
	"k8s.io/apimachinery/pkg/util/sets"
	"k8s.io/klog"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/manager"
)

var allControllers = []string{
	"application",
	"resourcecost",
}

// setup all available controllers one by one
func addAllControllers(mgr manager.Manager, cmOptions *options.ControllerManagerOptions) error {

	if cmOptions.IsControllerEnabled("application") {
		reconcile := &controller.ApplicationReconciler{
			Client:            mgr.GetClient(),
			Scheme:            mgr.GetScheme(),
			PrometheusAddress: config.SC.Prometheus,
			Log:               ctrl.Log.WithName("controllers").WithName("application"),
			HostSuffix:        fmt.Sprintf("%s%s", config.EC.Cluster.IP, config.SC.HostSuffix),
		}
		addControllerWithSetup(mgr, "application", reconcile)
	}

	if cmOptions.IsControllerEnabled("resourcecost") {
		reconcile := &controller.ResourceCostReconciler{
			Client: mgr.GetClient(),
			Scheme: mgr.GetScheme(),
			Log:    ctrl.Log.WithName("controllers").WithName("resourcecost"),
		}
		addControllerWithSetup(mgr, "resourcecost", reconcile)
	}

	// log all controllers process result
	for _, name := range allControllers {
		if cmOptions.IsControllerEnabled(name) {
			if addSuccessfullyControllers.Has(name) {
				klog.Infof("%s controller is enabled and added successfully.", name)
			} else {
				klog.Infof("%s controller is enabled but is not going to run due to its dependent component being disabled.", name)
			}
		} else {
			klog.Infof("%s controller is disabled by controller selectors.", name)
		}
	}
	return nil
}

var addSuccessfullyControllers = sets.New[string]()

type setupableController interface {
	SetupWithManager(mgr ctrl.Manager) error
}

func addControllerWithSetup(mgr manager.Manager, name string, controller setupableController) {
	if err := controller.SetupWithManager(mgr); err != nil {
		klog.Fatalf("Unable to create %v controller: %v", name, err)
	}
	addSuccessfullyControllers.Insert(name)
}

// controller-runtime
//func addController(mgr manager.Manager, name string, controller manager.Runnable) {
//	if err := mgr.Add(controller); err != nil {
//		klog.Fatalf("Unable to create %v controller: %v", name, err)
//	}
//	addSuccessfullyControllers.Insert(name)
//}
