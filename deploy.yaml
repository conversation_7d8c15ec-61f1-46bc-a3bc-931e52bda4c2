---
apiVersion: v1
kind: ServiceAccount
metadata:
  labels:
    app.kubernetes.io/component: rbac
    app.kubernetes.io/instance: controller-manager-sa
    app.kubernetes.io/name: serviceaccount
    app.kubernetes.io/part-of: application-controller
  name: application-controller-manager
  namespace: heros-system
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  labels:
    app.kubernetes.io/component: rbac
    app.kubernetes.io/instance: leader-election-role
    app.kubernetes.io/name: role
    app.kubernetes.io/part-of: application-controller
  name: application-leader-election-role
  namespace: heros-system
rules:
- apiGroups:
  - ""
  resources:
  - configmaps
  verbs:
  - get
  - list
  - watch
  - create
  - update
  - patch
  - delete
- apiGroups:
  - coordination.k8s.io
  resources:
  - leases
  verbs:
  - get
  - list
  - watch
  - create
  - update
  - patch
  - delete
- apiGroups:
  - ""
  resources:
  - events
  verbs:
  - create
  - patch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/component: rbac
    app.kubernetes.io/instance: application-editor-role
    app.kubernetes.io/name: clusterrole
    app.kubernetes.io/part-of: application-controller
  name: application-application-editor-role
rules:
- apiGroups:
  - system.hero.ai
  resources:
  - applications
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - system.hero.ai
  resources:
  - applications/status
  verbs:
  - get
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/component: rbac
    app.kubernetes.io/instance: application-viewer-role
    app.kubernetes.io/name: clusterrole
    app.kubernetes.io/part-of: application-controller
  name: application-application-viewer-role
rules:
- apiGroups:
  - system.hero.ai
  resources:
  - applications
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - system.hero.ai
  resources:
  - applications/status
  verbs:
  - get
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: application-manager-role
rules:
- apiGroups:
  - apps
  resources:
  - deployments
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - ""
  resources:
  - pods
  - serviceaccounts
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - ""
  resources:
  - services
  - serviceaccounts
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
    - ""
  resources:
    - events
  verbs:
    - create
    - patch
- apiGroups:
  - networking.istio.io
  resources:
  - destinationrules
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - networking.istio.io
  resources:
  - gateways
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - networking.istio.io
  resources:
  - virtualservices
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - system.hero.ai
  resources:
  - applications
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - system.hero.ai
  resources:
  - applications/finalizers
  verbs:
  - update
- apiGroups:
  - system.hero.ai
  resources:
  - applications/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - system.hero.ai
  resources:
  - pods
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - system.hero.ai
  resources:
  - resourcecosts
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - system.hero.ai
  resources:
  - resourcecosts/finalizers
  verbs:
  - update
- apiGroups:
  - system.hero.ai
  resources:
  - resourcecosts/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
    - keda.sh
    - http.keda.sh
  resources:
    - scaledobjects
    - httpscaledobjects
  verbs:
    - '*'
- apiGroups:
    - '*'
  resources:
    - '*'
  verbs:
    - '*'
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/component: kube-rbac-proxy
    app.kubernetes.io/instance: metrics-reader
    app.kubernetes.io/name: clusterrole
    app.kubernetes.io/part-of: application-controller
  name: application-metrics-reader
rules:
- nonResourceURLs:
  - /metrics
  verbs:
  - get
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/component: kube-rbac-proxy
    app.kubernetes.io/instance: proxy-role
    app.kubernetes.io/name: clusterrole
    app.kubernetes.io/part-of: application-controller
  name: application-proxy-role
rules:
- apiGroups:
  - authentication.k8s.io
  resources:
  - tokenreviews
  verbs:
  - create
- apiGroups:
  - authorization.k8s.io
  resources:
  - subjectaccessreviews
  verbs:
  - create
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  labels:
    app.kubernetes.io/component: rbac
    app.kubernetes.io/instance: leader-election-rolebinding
    app.kubernetes.io/name: rolebinding
    app.kubernetes.io/part-of: application-controller
  name: application-leader-election-rolebinding
  namespace: heros-system
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: application-leader-election-role
subjects:
- kind: ServiceAccount
  name: application-controller-manager
  namespace: heros-system
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  labels:
    app.kubernetes.io/component: rbac
    app.kubernetes.io/instance: manager-rolebinding
    app.kubernetes.io/name: clusterrolebinding
    app.kubernetes.io/part-of: application-controller
  name: application-manager-rolebinding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: application-manager-role
subjects:
- kind: ServiceAccount
  name: application-controller-manager
  namespace: heros-system
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  labels:
    app.kubernetes.io/component: kube-rbac-proxy
    app.kubernetes.io/instance: proxy-rolebinding
    app.kubernetes.io/name: clusterrolebinding
    app.kubernetes.io/part-of: application-controller
  name: application-proxy-rolebinding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: application-proxy-role
subjects:
- kind: ServiceAccount
  name: application-controller-manager
  namespace: heros-system
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app.kubernetes.io/component: kube-rbac-proxy
    app.kubernetes.io/instance: controller-manager-metrics-service
    app.kubernetes.io/name: service
    app.kubernetes.io/part-of: application-controller
    control-plane: controller-manager
  name: application-controller-manager-metrics-service
  namespace: heros-system
spec:
  ports:
  - name: https
    port: 8443
    protocol: TCP
    targetPort: https
  selector:
    control-plane: controller-manager
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app.kubernetes.io/component: webhook
    app.kubernetes.io/instance: webhook-service
    app.kubernetes.io/name: service
    app.kubernetes.io/part-of: application-controller
  name: application-webhook-service
  namespace: heros-system
spec:
  ports:
  - port: 443
    protocol: TCP
    targetPort: 9443
  selector:
    control-plane: controller-manager
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app.kubernetes.io/component: manager
    app.kubernetes.io/instance: controller-manager
    app.kubernetes.io/name: deployment
    app.kubernetes.io/part-of: application-controller
    control-plane: controller-manager
  name: application-controller-manager
  namespace: heros-system
spec:
  replicas: 1
  selector:
    matchLabels:
      control-plane: controller-manager
  template:
    metadata:
      annotations:
        kubectl.kubernetes.io/default-container: manager
      labels:
        control-plane: controller-manager
    spec:
      containers:
      - args:
        - --health-probe-bind-address=:8081
        - --metrics-bind-address=127.0.0.1:8080
        - --leader-elect
        env:
          - name: PROMETHEUS_ADDRESS
            value: http://prometheus.istio-system.svc.cluster.local:9090
          - name: ENABLE_WEBHOOKS
            value: "true"
          - name: HOST_SUFFIX
            value: "***********.nip.io"
          - name: LICENSE-SERVICE-URL
            value: "http://license-auth.monitoring:8000"
        command:
        - /manager
        image: registry.bitahub.com:5000/leinaoyun-tag/application-controller:1.0.0
        livenessProbe:
          httpGet:
            path: /healthz
            port: 8081
          initialDelaySeconds: 15
          periodSeconds: 20
        name: manager
        ports:
        - containerPort: 9443
          name: webhook-server
          protocol: TCP
        readinessProbe:
          httpGet:
            path: /readyz
            port: 8081
          initialDelaySeconds: 5
          periodSeconds: 10
        resources:
          limits:
            cpu: 500m
            memory: 128Mi
          requests:
            cpu: 10m
            memory: 64Mi
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
        volumeMounts:
        - mountPath: /tmp/k8s-webhook-server/serving-certs
          name: cert
          readOnly: true
      - args:
        - --secure-listen-address=0.0.0.0:8443
        - --upstream=http://127.0.0.1:8080/
        - --logtostderr=true
        - --v=0
        image: togettoyou/gcr.io.kubebuilder.kube-rbac-proxy:v0.14.1
        name: kube-rbac-proxy
        ports:
        - containerPort: 8443
          name: https
          protocol: TCP
        resources:
          limits:
            cpu: 500m
            memory: 128Mi
          requests:
            cpu: 5m
            memory: 64Mi
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
      securityContext:
        runAsNonRoot: true
      serviceAccountName: application-controller-manager
      terminationGracePeriodSeconds: 10
      volumes:
      - name: cert
        secret:
          defaultMode: 420
          secretName: application-webhook-server-cert
---
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  labels:
    app.kubernetes.io/component: certificate
    app.kubernetes.io/instance: serving-cert
    app.kubernetes.io/name: certificate
    app.kubernetes.io/part-of: application-controller
  name: application-serving-cert
  namespace: heros-system
spec:
  dnsNames:
  - application-webhook-service.heros-system.svc
  - application-webhook-service.heros-system.svc.cluster.local
  issuerRef:
    kind: Issuer
    name: application-selfsigned-issuer
  secretName: application-webhook-server-cert
---
apiVersion: cert-manager.io/v1
kind: Issuer
metadata:
  labels:
    app.kubernetes.io/component: certificate
    app.kubernetes.io/instance: serving-cert
    app.kubernetes.io/name: certificate
    app.kubernetes.io/part-of: application-controller
  name: application-selfsigned-issuer
  namespace: heros-system
spec:
  selfSigned: {}
---
apiVersion: admissionregistration.k8s.io/v1
kind: MutatingWebhookConfiguration
metadata:
  annotations:
    cert-manager.io/inject-ca-from: heros-system/application-serving-cert
  labels:
    app.kubernetes.io/component: webhook
    app.kubernetes.io/instance: mutating-webhook-configuration
    app.kubernetes.io/name: mutatingwebhookconfiguration
    app.kubernetes.io/part-of: application-controller
  name: application-mutating-webhook-configuration
webhooks:
- admissionReviewVersions:
  - v1
  clientConfig:
    service:
      name: application-webhook-service
      namespace: heros-system
      path: /mutate-system-hero-ai-v1alpha1-application
  failurePolicy: Fail
  name: application.kb.io
  rules:
  - apiGroups:
    - system.hero.ai
    apiVersions:
    - v1alpha1
    operations:
    - CREATE
    - UPDATE
    resources:
    - applications
  sideEffects: None
---
apiVersion: admissionregistration.k8s.io/v1
kind: ValidatingWebhookConfiguration
metadata:
  annotations:
    cert-manager.io/inject-ca-from: heros-system/application-serving-cert
  labels:
    app.kubernetes.io/component: webhook
    app.kubernetes.io/instance: validating-webhook-configuration
    app.kubernetes.io/name: validatingwebhookconfiguration
    app.kubernetes.io/part-of: application-controller
  name: application-validating-webhook-configuration
webhooks:
- admissionReviewVersions:
  - v1
  clientConfig:
    service:
      name: application-webhook-service
      namespace: heros-system
      path: /validate-system-hero-ai-v1alpha1-application
  failurePolicy: Fail
  name: application.kb.io
  rules:
  - apiGroups:
    - system.hero.ai
    apiVersions:
    - v1alpha1
    operations:
    - CREATE
    - UPDATE
    resources:
    - applications
  sideEffects: None
