# permissions for end users to edit applications.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: clusterrole
    app.kubernetes.io/instance: application-editor-role
    app.kubernetes.io/component: rbac
    app.kubernetes.io/created-by: application-controller
    app.kubernetes.io/part-of: application-controller
    app.kubernetes.io/managed-by: kustomize
  name: application-editor-role
rules:
- apiGroups:
  - system.hero.ai
  resources:
  - applications
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - system.hero.ai
  resources:
  - applications/status
  verbs:
  - get
