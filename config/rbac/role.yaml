---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: manager-role
rules:
- apiGroups:
  - apps
  resources:
  - deployments
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - ""
  resources:
  - pods
  - serviceaccounts
  - services
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - http.keda.sh
  resources:
  - httpscaledobjects
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - keda.sh
  resources:
  - scaledobjects
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - networking.istio.io
  resources:
  - destinationrules
  - gateways
  - virtualservices
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - system.hero.ai
  resources:
  - applications
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - system.hero.ai
  resources:
  - applications/finalizers
  verbs:
  - update
- apiGroups:
  - system.hero.ai
  resources:
  - applications/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - system.hero.ai
  resources:
  - pods
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - system.hero.ai
  resources:
  - resourcecosts
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - system.hero.ai
  resources:
  - resourcecosts/finalizers
  verbs:
  - update
- apiGroups:
  - system.hero.ai
  resources:
  - resourcecosts/status
  verbs:
  - get
  - patch
  - update
