# permissions for end users to view applications.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: clusterrole
    app.kubernetes.io/instance: application-viewer-role
    app.kubernetes.io/component: rbac
    app.kubernetes.io/created-by: application-controller
    app.kubernetes.io/part-of: application-controller
    app.kubernetes.io/managed-by: kustomize
  name: application-viewer-role
rules:
- apiGroups:
  - system.hero.ai
  resources:
  - applications
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - system.hero.ai
  resources:
  - applications/status
  verbs:
  - get
