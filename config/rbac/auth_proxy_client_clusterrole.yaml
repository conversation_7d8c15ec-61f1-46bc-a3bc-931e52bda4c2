apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: clusterrole
    app.kubernetes.io/instance: metrics-reader
    app.kubernetes.io/component: kube-rbac-proxy
    app.kubernetes.io/created-by: application-controller
    app.kubernetes.io/part-of: application-controller
    app.kubernetes.io/managed-by: kustomize
  name: metrics-reader
rules:
- nonResourceURLs:
  - "/metrics"
  verbs:
  - get
