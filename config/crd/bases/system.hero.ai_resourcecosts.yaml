---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.13.0
  name: resourcecosts.system.hero.ai
spec:
  group: system.hero.ai
  names:
    kind: ResourceCost
    listKind: ResourceCostList
    plural: resourcecosts
    shortNames:
    - rsct
    singular: resourcecost
  scope: Namespaced
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        properties:
          apiVersion:
            type: string
          kind:
            type: string
          metadata:
            type: object
          spec:
            properties:
              current:
                properties:
                  pods:
                    additionalProperties:
                      items:
                        properties:
                          containerName:
                            type: string
                          containerStatus:
                            type: string
                          creatingTime:
                            format: date-time
                            type: string
                          name:
                            type: string
                          nodeName:
                            type: string
                          resource:
                            additionalProperties:
                              anyOf:
                              - type: integer
                              - type: string
                              pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                              x-kubernetes-int-or-string: true
                            type: object
                          runningTime:
                            format: date-time
                            type: string
                          stoppedTime:
                            format: date-time
                            type: string
                        type: object
                      type: array
                    type: object
                type: object
            type: object
          status:
            properties:
              pods:
                additionalProperties:
                  items:
                    properties:
                      containerName:
                        type: string
                      containerStatus:
                        type: string
                      creatingTime:
                        format: date-time
                        type: string
                      name:
                        type: string
                      nodeName:
                        type: string
                      resource:
                        additionalProperties:
                          anyOf:
                          - type: integer
                          - type: string
                          pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                          x-kubernetes-int-or-string: true
                        type: object
                      runningTime:
                        format: date-time
                        type: string
                      stoppedTime:
                        format: date-time
                        type: string
                    type: object
                  type: array
                type: object
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
