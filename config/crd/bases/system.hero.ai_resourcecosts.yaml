---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.11.1
  creationTimestamp: null
  name: resourcecosts.system.hero.ai
spec:
  group: system.hero.ai
  names:
    kind: ResourceCost
    listKind: ResourceCostList
    plural: resourcecosts
    shortNames:
    - rsct
    singular: resourcecost
  scope: Namespaced
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        description: ResourceCost is the Schema for the resourceCosts API
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: ResourceCostSpec defines the desired state of ResourceCost
            properties:
              current:
                properties:
                  pods:
                    additionalProperties:
                      items:
                        properties:
                          containerName:
                            type: string
                          containerStatus:
                            type: string
                          creatingTime:
                            format: date-time
                            type: string
                          name:
                            type: string
                          nodeName:
                            type: string
                          resource:
                            additionalProperties:
                              anyOf:
                              - type: integer
                              - type: string
                              pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                              x-kubernetes-int-or-string: true
                            description: ResourceList is a set of (resource name,
                              quantity) pairs.
                            type: object
                          runningTime:
                            format: date-time
                            type: string
                          stoppedTime:
                            format: date-time
                            type: string
                        type: object
                      type: array
                    type: object
                type: object
            type: object
          status:
            description: ResourceCostStatus defines the observed state of ResourceCost
            properties:
              pods:
                additionalProperties:
                  items:
                    properties:
                      containerName:
                        type: string
                      containerStatus:
                        type: string
                      creatingTime:
                        format: date-time
                        type: string
                      name:
                        type: string
                      nodeName:
                        type: string
                      resource:
                        additionalProperties:
                          anyOf:
                          - type: integer
                          - type: string
                          pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                          x-kubernetes-int-or-string: true
                        description: ResourceList is a set of (resource name, quantity)
                          pairs.
                        type: object
                      runningTime:
                        format: date-time
                        type: string
                      stoppedTime:
                        format: date-time
                        type: string
                    type: object
                  type: array
                type: object
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
