
apiVersion: v1
kind: Service
metadata:
  labels:
    app.kubernetes.io/name: service
    app.kubernetes.io/instance: webhook-service
    app.kubernetes.io/component: webhook
    app.kubernetes.io/created-by: application-controller
    app.kubernetes.io/part-of: application-controller
    app.kubernetes.io/managed-by: kustomize
  name: webhook-service
  namespace: system
spec:
  ports:
    - port: 443
      protocol: TCP
      targetPort: 9443
  selector:
    control-plane: controller-manager
