image:
  image: registry.bitahub.com:5000/leinaoyun-tag/application-controller
  tag: "1.0.0"
  pullPolicy: Always

replicaCount: 1

config:
  prometheus: http://prometheus-k8s.monitoring.svc.cluster.local:9090
  webhooks: "true"
  hostSuffix: "***********.nip.io"
  licenseServiceUrl: "http://license-auth.monitoring:8000"

livenessProbe:
  httpGet:
    path: /healthz
    port: 8081
  initialDelaySeconds: 15
  periodSeconds: 20

readinessProbe:
  httpGet:
    path: /readyz
    port: 8081
  initialDelaySeconds: 5
  periodSeconds: 10

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

podAnnotations: {}

podSecurityContext: {}
# fsGroup: 2000

securityContext: {}

tolerations:
  - key: node-role.k8s.io/master
    effect: NoSchedule
  - key: CriticalAddonsOnly
    operator: Exists
  - effect: NoExecute
    key: node.k8s.io/not-ready
    operator: Exists
    tolerationSeconds: 60
  - effect: NoExecute
    key: node.k8s.io/unreachable
    operator: Exists
    tolerationSeconds: 60

affinity: {}
