apiVersion: v1
kind: ServiceAccount
metadata:
  labels:
    app.kubernetes.io/component: rbac
    app.kubernetes.io/instance: controller-manager-sa
    app.kubernetes.io/name: serviceaccount
    app.kubernetes.io/part-of: application-controller
  name: application-controller-manager
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  labels:
    app.kubernetes.io/component: rbac
    app.kubernetes.io/instance: leader-election-role
    app.kubernetes.io/name: role
    app.kubernetes.io/part-of: application-controller
  name: application-leader-election-role
rules:
  - apiGroups:
      - ""
    resources:
      - configmaps
    verbs:
      - get
      - list
      - watch
      - create
      - update
      - patch
      - delete
  - apiGroups:
      - coordination.k8s.io
    resources:
      - leases
    verbs:
      - get
      - list
      - watch
      - create
      - update
      - patch
      - delete
  - apiGroups:
      - ""
    resources:
      - events
    verbs:
      - create
      - patch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/component: rbac
    app.kubernetes.io/instance: application-editor-role
    app.kubernetes.io/name: clusterrole
    app.kubernetes.io/part-of: application-controller
  name: application-application-editor-role
rules:
  - apiGroups:
      - system.hero.ai
    resources:
      - applications
    verbs:
      - create
      - delete
      - get
      - list
      - patch
      - update
      - watch
  - apiGroups:
      - system.hero.ai
    resources:
      - applications/status
    verbs:
      - get
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/component: rbac
    app.kubernetes.io/instance: application-viewer-role
    app.kubernetes.io/name: clusterrole
    app.kubernetes.io/part-of: application-controller
  name: application-application-viewer-role
rules:
  - apiGroups:
      - system.hero.ai
    resources:
      - applications
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - system.hero.ai
    resources:
      - applications/status
    verbs:
      - get
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: application-manager-role
rules:
  - apiGroups:
      - apps
    resources:
      - deployments
    verbs:
      - create
      - delete
      - get
      - list
      - patch
      - update
      - watch
  - apiGroups:
      - ""
    resources:
      - pods
      - serviceaccounts
    verbs:
      - create
      - delete
      - get
      - list
      - patch
      - update
      - watch
  - apiGroups:
      - ""
    resources:
      - services
      - serviceaccounts
    verbs:
      - create
      - delete
      - get
      - list
      - patch
      - update
      - watch
  - apiGroups:
      - ""
    resources:
      - events
    verbs:
      - create
      - patch
  - apiGroups:
      - networking.istio.io
    resources:
      - destinationrules
    verbs:
      - create
      - delete
      - get
      - list
      - patch
      - update
      - watch
  - apiGroups:
      - networking.istio.io
    resources:
      - gateways
    verbs:
      - create
      - delete
      - get
      - list
      - patch
      - update
      - watch
  - apiGroups:
      - networking.istio.io
    resources:
      - virtualservices
    verbs:
      - create
      - delete
      - get
      - list
      - patch
      - update
      - watch
  - apiGroups:
      - system.hero.ai
    resources:
      - applications
    verbs:
      - create
      - delete
      - get
      - list
      - patch
      - update
      - watch
  - apiGroups:
      - system.hero.ai
    resources:
      - applications/finalizers
    verbs:
      - update
  - apiGroups:
      - system.hero.ai
    resources:
      - applications/status
    verbs:
      - get
      - patch
      - update
  - apiGroups:
      - system.hero.ai
    resources:
      - pods
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - system.hero.ai
    resources:
      - resourcecosts
    verbs:
      - create
      - delete
      - get
      - list
      - patch
      - update
      - watch
  - apiGroups:
      - system.hero.ai
    resources:
      - resourcecosts/finalizers
    verbs:
      - update
  - apiGroups:
      - system.hero.ai
    resources:
      - resourcecosts/status
    verbs:
      - get
      - patch
      - update
  - apiGroups:
      - keda.sh
      - http.keda.sh
    resources:
      - scaledobjects
      - httpscaledobjects
    verbs:
      - '*'
  - apiGroups:
      - '*'
    resources:
      - '*'
    verbs:
      - '*'
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/component: kube-rbac-proxy
    app.kubernetes.io/instance: metrics-reader
    app.kubernetes.io/name: clusterrole
    app.kubernetes.io/part-of: application-controller
  name: application-metrics-reader
rules:
  - nonResourceURLs:
      - /metrics
    verbs:
      - get
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/component: kube-rbac-proxy
    app.kubernetes.io/instance: proxy-role
    app.kubernetes.io/name: clusterrole
    app.kubernetes.io/part-of: application-controller
  name: application-proxy-role
rules:
  - apiGroups:
      - authentication.k8s.io
    resources:
      - tokenreviews
    verbs:
      - create
  - apiGroups:
      - authorization.k8s.io
    resources:
      - subjectaccessreviews
    verbs:
      - create
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  labels:
    app.kubernetes.io/component: rbac
    app.kubernetes.io/instance: leader-election-rolebinding
    app.kubernetes.io/name: rolebinding
    app.kubernetes.io/part-of: application-controller
  name: application-leader-election-rolebinding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: application-leader-election-role
subjects:
  - kind: ServiceAccount
    name: application-controller-manager
    namespace: {{ .Release.Namespace }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  labels:
    app.kubernetes.io/component: rbac
    app.kubernetes.io/instance: manager-rolebinding
    app.kubernetes.io/name: clusterrolebinding
    app.kubernetes.io/part-of: application-controller
  name: application-manager-rolebinding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: application-manager-role
subjects:
  - kind: ServiceAccount
    name: application-controller-manager
    namespace: {{ .Release.Namespace }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  labels:
    app.kubernetes.io/component: kube-rbac-proxy
    app.kubernetes.io/instance: proxy-rolebinding
    app.kubernetes.io/name: clusterrolebinding
    app.kubernetes.io/part-of: application-controller
  name: application-proxy-rolebinding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: application-proxy-role
subjects:
  - kind: ServiceAccount
    name: application-controller-manager
    namespace: {{ .Release.Namespace }}