apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app.kubernetes.io/component: manager
    app.kubernetes.io/instance: controller-manager
    app.kubernetes.io/name: deployment
    app.kubernetes.io/part-of: application-controller
    control-plane: controller-manager
  name: application-controller-manager
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      control-plane: controller-manager
  template:
    metadata:
      annotations:
        kubectl.kubernetes.io/default-container: manager
      labels:
        control-plane: controller-manager
    spec:
      containers:
        - args:
            - --health-probe-bind-address=:8081
            - --metrics-bind-address=127.0.0.1:8080
            - --zap-log-level=debug
            - --leader-elect
          env:
            - name: PROMETHEUS_ADDRESS
              value: {{ .Values.config.prometheus }}
            - name: ENABLE_WEBHOOKS
              value: {{ .Values.config.webhooks }}
            - name: HOST_SUFFIX
              value: {{ .Values.config.hostSuffix }}
            - name: LICENSE-SERVICE-URL
              value: {{ .Values.config.licenseServiceUrl}}
          command:
            - /manager
          image: {{ .Values.image.image }}:{{ .Values.image.tag | default .Chart.AppVersion }}
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          {{- with .Values.livenessProbe }}
          livenessProbe:
            {{- toYaml . | nindent 8 }}
          {{- end }}
          name: manager
          ports:
            - containerPort: 9443
              name: webhook-server
              protocol: TCP
          {{- with .Values.readinessProbe }}
          readinessProbe:
            {{- toYaml . | nindent 8 }}
          {{- end }}
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
                - ALL
          volumeMounts:
            - mountPath: /tmp/k8s-webhook-server/serving-certs
              name: cert
              readOnly: true
        - args:
            - --secure-listen-address=0.0.0.0:8443
            - --upstream=http://127.0.0.1:8080/
            - --logtostderr=true
            - --v=0
          image: togettoyou/gcr.io.kubebuilder.kube-rbac-proxy:v0.14.1
          name: kube-rbac-proxy
          ports:
            - containerPort: 8443
              name: https
              protocol: TCP
          resources:
            limits:
              cpu: 500m
              memory: 128Mi
            requests:
              cpu: 5m
              memory: 64Mi
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
                - ALL
      securityContext:
        runAsNonRoot: true
      serviceAccountName: application-controller-manager
      terminationGracePeriodSeconds: 10
      volumes:
        - name: cert
          secret:
            defaultMode: 420
            secretName: application-webhook-server-cert