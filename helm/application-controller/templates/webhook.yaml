{{- $ca := genCA "application-admission-ca" 3650 }}
{{- $cn := printf "%s-webhook-service" .Release.Name }}
{{- $altName1 := printf "application-webhook-service.%s" .Release.Namespace }}
{{- $altName2 := printf "application-webhook-service.%s.svc" .Release.Namespace }}
{{- $altName3 := printf "application-webhook-service.%s.svc.cluster.local" .Release.Namespace }}
{{- $cert := genSignedCert $cn nil (list $altName1 $altName2 $altName3) 3650 $ca -}}
---
apiVersion: v1
kind: Secret
metadata:
  namespace: {{ .Release.Namespace }}
  name: application-webhook-server-cert
type: kubernetes.io/tls
stringData:
  tls.crt: {{ $cert.Cert | quote }}
  tls.key: {{ $cert.Key | quote }}
---
apiVersion: admissionregistration.k8s.io/v1
kind: MutatingWebhookConfiguration
metadata:
  annotations:
    cert-manager.io/inject-ca-from: {{ .Release.Namespace }}/application-serving-cert
  labels:
    app.kubernetes.io/component: webhook
    app.kubernetes.io/instance: mutating-webhook-configuration
    app.kubernetes.io/name: mutatingwebhookconfiguration
    app.kubernetes.io/part-of: application-controller
  name: application-mutating-webhook-configuration
webhooks:
  - admissionReviewVersions:
      - v1
    clientConfig:
      service:
        name: application-webhook-service
        namespace: {{ .Release.Namespace }}
        path: /mutate-system-hero-ai-v1alpha1-application
      caBundle: {{ b64enc $ca.Cert | quote }}
    failurePolicy: Fail
    name: mapplication.kb.io
    rules:
      - apiGroups:
          - system.hero.ai
        apiVersions:
          - v1alpha1
        operations:
          - CREATE
          - UPDATE
        resources:
          - applications
    sideEffects: None
---
apiVersion: admissionregistration.k8s.io/v1
kind: ValidatingWebhookConfiguration
metadata:
  annotations:
    cert-manager.io/inject-ca-from: {{ .Release.Namespace }}/application-serving-cert
  labels:
    app.kubernetes.io/component: webhook
    app.kubernetes.io/instance: validating-webhook-configuration
    app.kubernetes.io/name: validatingwebhookconfiguration
    app.kubernetes.io/part-of: application-controller
  name: application-validating-webhook-configuration
webhooks:
  - admissionReviewVersions:
      - v1
    clientConfig:
      caBundle: {{ b64enc $ca.Cert | quote }}
      service:
        name: application-webhook-service
        namespace: {{ .Release.Namespace }}
        path: /validate-system-hero-ai-v1alpha1-application
    failurePolicy: Fail
    name: vapplication.kb.io
    rules:
      - apiGroups:
          - system.hero.ai
        apiVersions:
          - v1alpha1
        operations:
          - CREATE
          - UPDATE
        resources:
          - applications
    sideEffects: None