apiVersion: v1
kind: Service
metadata:
  labels:
    app.kubernetes.io/component: kube-rbac-proxy
    app.kubernetes.io/instance: controller-manager-metrics-service
    app.kubernetes.io/name: service
    app.kubernetes.io/part-of: application-controller
    control-plane: controller-manager
  name: application-controller-manager-metrics-service
spec:
  ports:
    - name: https
      port: 8443
      protocol: TCP
      targetPort: https
  selector:
    control-plane: controller-manager
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app.kubernetes.io/component: webhook
    app.kubernetes.io/instance: webhook-service
    app.kubernetes.io/name: service
    app.kubernetes.io/part-of: application-controller
  name: application-webhook-service
spec:
  ports:
    - port: 443
      protocol: TCP
      targetPort: 9443
  selector:
    control-plane: controller-manager