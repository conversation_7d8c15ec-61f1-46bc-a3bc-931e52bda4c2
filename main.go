package main

import (
	"fmt"
	"regexp"
)

func hasPrefixRegex(s, prefix string) bool {
	// 允许前缀带引号
	re := regexp.MustCompile(`^["']?` + regexp.QuoteMeta(prefix))
	return re.MatchString(s)
}
func main() {
	s := `"/bin/bash", "-c", "./script.sh"`
	prefix := "/bin/bash"
	//s = strings.ReplaceAll(s, ",", "")
	//s = strings.TrimSpace(s)
	if hasPrefixRegex(s, prefix) {
		fmt.Println("Prefix matched!")
	} else {
		fmt.Println("No match.")
	}
}

//import (
//	"fmt"
//	"strings"
//)
//
//func main() {
//	s := `"/bin/bash -c ./start_service.sh"`
//	if strings.HasPrefix(s, "/bin/bash") {
//		fmt.Println("yeeee")
//	} else {
//		fmt.Println("nooooo")
//	}
//}
