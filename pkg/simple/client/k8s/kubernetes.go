package k8s

import (
	istioclient "istio.io/client-go/pkg/clientset/versioned"
	apiextensionsclient "k8s.io/apiextensions-apiserver/pkg/client/clientset/clientset"
	"k8s.io/client-go/discovery"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
)

type Client interface {
	Kubernetes() kubernetes.Interface
	APIExtensions() apiextensionsclient.Interface
	Istio() istioclient.Interface
	Discovery() discovery.DiscoveryInterface
	Master() string
	Config() *rest.Config
}

type kubernetesClient struct {
	// kubernetes client interface
	k8s kubernetes.Interface

	// discovery client
	discoveryClient *discovery.DiscoveryClient

	apiextensions apiextensionsclient.Interface

	istio istioclient.Interface

	master string

	config *rest.Config
}

// NewKubernetesClient creates a KubernetesClient
func NewKubernetesClient(options *KubernetesOptions) (Client, error) {
	options.KubeConfig = "C:\\Users\\<USER>\\.kube\\config"
	config, err := clientcmd.BuildConfigFromFlags("", options.KubeConfig)
	if err != nil {
		return nil, err
	}

	config.QPS = options.QPS
	config.Burst = options.Burst

	var k8sClient kubernetesClient
	k8sClient.k8s, err = kubernetes.NewForConfig(config)
	if err != nil {
		return nil, err
	}

	k8sClient.istio, err = istioclient.NewForConfig(config)

	if err != nil {
		return nil, err
	}

	k8sClient.discoveryClient, err = discovery.NewDiscoveryClientForConfig(config)
	if err != nil {
		return nil, err
	}

	k8sClient.apiextensions, err = apiextensionsclient.NewForConfig(config)
	if err != nil {
		return nil, err
	}

	k8sClient.master = options.Master
	k8sClient.config = config

	return &k8sClient, nil
}

func (k *kubernetesClient) Istio() istioclient.Interface {
	return k.istio
}

func (k *kubernetesClient) Kubernetes() kubernetes.Interface {
	return k.k8s
}

func (k *kubernetesClient) Discovery() discovery.DiscoveryInterface {
	return k.discoveryClient
}

func (k *kubernetesClient) APIExtensions() apiextensionsclient.Interface {
	return k.apiextensions
}

func (k *kubernetesClient) Master() string {
	return k.master
}

func (k *kubernetesClient) Config() *rest.Config {
	return k.config
}
