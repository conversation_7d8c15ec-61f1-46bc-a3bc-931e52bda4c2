package controller

import (
	appv1alpha1 "gitlab.leinao.ai/application-controller/apis/application/v1alpha1"
)

var app *appv1alpha1.Application
var reconciler ResourceCostReconciler

// func BeforeDate() {
// 	var scheme = runtime.NewScheme()
// 	utilruntime.Must(clientgoscheme.AddToScheme(scheme))
// 	utilruntime.Must(clientnetworking.AddToScheme(scheme))
// 	utilruntime.Must(appv1alpha1.AddToScheme(scheme))
// 	var num int32 = 1
// 	app = &appv1alpha1.Application{
// 		ObjectMeta: metav1.ObjectMeta{
// 			Name:      "app",
// 			Namespace: "app",
// 		},

// 		Spec: appv1alpha1.ApplicationSpec{
// 			AutoScaling: &appv1alpha1.AutoScalingSpec{
// 				Backend:        appv1alpha1.Keda,
// 				MinReplicas:    &num,
// 				MaxReplicas:    &num,
// 				CooldownPeriod: &num,
// 				Triggers: []appv1alpha1.ScaleTriggers{
// 					{
// 						Type:     "cpu",
// 						Metadata: map[string]string{"Utilization": "50"},
// 					},
// 				},
// 				Serverless: []appv1alpha1.ScaleTriggers{
// 					{
// 						Type:     "prometheus",
// 						Metadata: map[string]string{"http_requests_total": "20"},
// 					},
// 				},
// 			},
// 			Version: "v1",
// 			ServiceMesh: &appv1alpha1.ServiceMeshSpec{
// 				Backend: appv1alpha1.Istio,
// 				SubRoute: &appv1alpha1.SubRoute{
// 					Weight: &num,
// 					Retries: &appv1alpha1.HTTPRetry{
// 						AutoRetire: true,
// 						Attempts:   3,
// 					},
// 					TrafficPolicy: &appv1alpha1.TrafficPolicy{
// 						LoadBalancer: 4,
// 						CircuitBreaker: &appv1alpha1.CircuitBreaker{
// 							MaxConnections:          100,
// 							HTTP1MaxPendingRequests: 100,
// 							ConsecutiveErrors:       5,
// 							Interval:                &metav1.Duration{Duration: time.Second * 5},
// 							BaseEjectionTime:        &metav1.Duration{Duration: time.Minute * 5},
// 							MaxEjectionPercent:      100,
// 						},
// 					},
// 				},
// 			},
// 			Server: map[string][]appv1alpha1.ServerSpec{
// 				"app": {
// 					{
// 						Version:  "v1",
// 						Replicas: pointer.Int32Ptr(1),
// 						Template: corev1.PodTemplateSpec{
// 							ObjectMeta: metav1.ObjectMeta{
// 								Labels: map[string]string{"app": "image-describe"},
// 							},
// 							Spec: corev1.PodSpec{
// 								Containers: []corev1.Container{
// 									{
// 										Name:            "image-describe",
// 										Image:           "registry.cn-hangzhou.aliyuncs.com/ruanxingbaozi/pytorch:image_recognition_demo",
// 										ImagePullPolicy: corev1.PullAlways,
// 										Args:            []string{"bash", "-c", "python image_recognition_server.py"},
// 										Ports: []corev1.ContainerPort{
// 											{
// 												Name:          "http",
// 												ContainerPort: 5000,
// 											},
// 										},
// 									},
// 								},
// 							},
// 						},
// 						ServiceMesh: &appv1alpha1.ServiceMeshSpec{
// 							Backend: appv1alpha1.Istio,
// 							SubRoute: &appv1alpha1.SubRoute{
// 								Weight: &num,
// 								Retries: &appv1alpha1.HTTPRetry{
// 									AutoRetire: true,
// 									Attempts:   3,
// 								},
// 								TrafficPolicy: &appv1alpha1.TrafficPolicy{
// 									LoadBalancer: 4,
// 									CircuitBreaker: &appv1alpha1.CircuitBreaker{
// 										MaxConnections:          100,
// 										HTTP1MaxPendingRequests: 100,
// 										ConsecutiveErrors:       5,
// 										Interval:                &metav1.Duration{Duration: time.Second * 5},
// 										BaseEjectionTime:        &metav1.Duration{Duration: time.Minute * 5},
// 										MaxEjectionPercent:      100,
// 									},
// 								},
// 							},
// 						},
// 					},
// 				},
// 			},
// 		},
// 		Status: appv1alpha1.ApplicationStatus{
// 			Phase: appv1alpha1.Running,
// 		},
// 	}

// 	pod := &corev1.Pod{
// 		ObjectMeta: metav1.ObjectMeta{
// 			Annotations: map[string]string{
// 				appv1alpha1.LabelApplicationJobID: "app",
// 			},
// 			Name:      "app",
// 			Namespace: "app",
// 			Labels: map[string]string{
// 				appv1alpha1.LabelApplicationName: "app",
// 				appv1alpha1.LabelAppName:         "app",
// 				appv1alpha1.LabelAppversion:      "v1",
// 			},
// 		},
// 		Spec: corev1.PodSpec{
// 			Containers: []corev1.Container{
// 				{
// 					Name:  "app",
// 					Image: "registry.cn-hangzhou.aliyuncs.com/ruanxingbaozi/pytorch:image_recognition_demo",
// 				},
// 			},
// 			NodeName: "node1",
// 		},
// 		Status: corev1.PodStatus{
// 			Phase: corev1.PodRunning,
// 			Conditions: []corev1.PodCondition{
// 				{
// 					Type:   corev1.PodInitialized,
// 					Status: corev1.ConditionTrue,
// 				},
// 				{
// 					Type:   corev1.PodReady,
// 					Status: corev1.ConditionTrue,
// 				},
// 			},
// 			ContainerStatuses: []corev1.ContainerStatus{
// 				{
// 					Name:         "app",
// 					RestartCount: 1,
// 					State: corev1.ContainerState{
// 						Running: &corev1.ContainerStateRunning{
// 							StartedAt: metav1.Now(),
// 						},
// 					},
// 				},
// 			},
// 		},
// 	}
// 	cost := &appv1alpha1.ResourceCost{
// 		ObjectMeta: metav1.ObjectMeta{
// 			Name:      "app",
// 			Namespace: "app",
// 		},
// 	}
// 	objs := []client.Object{
// 		app, pod, cost,
// 	}

// 	reconciler = ResourceCostReconciler{
// 		Client: fake.NewClientBuilder().WithScheme(scheme).WithObjects(objs...).WithIndex(&corev1.Pod{}, appv1alpha1.AppIndexField, func(object client.Object) []string {
// 			podObject := object.(*corev1.Pod)
// 			if appName, found := podObject.Labels[appv1alpha1.LabelApplicationName]; found {
// 				return []string{appName}
// 			}
// 			return nil
// 		}).Build(),
// 		Scheme:   scheme,
// 		Recorder: record.NewFakeRecorder(20),
// 	}
// }
// func Test_Reconciler(t *testing.T) {
// 	BeforeDate()

// 	pods := &corev1.PodList{}
// 	if err := reconciler.List(ctx, pods, client.InNamespace("app"), client.MatchingFields{appv1alpha1.AppIndexField: "app"}); err != nil {
// 		t.Errorf("Unexpected error: %v", err)
// 	}
// 	app = &appv1alpha1.Application{}
// 	if err := reconciler.Get(ctx, types.NamespacedName{Name: "app", Namespace: "app"}, app); err != nil {
// 		t.Errorf("Unexpected error: %v", err)
// 	}
// 	serverStatus := reconciler.buildServerStatus(pods, app)
// 	reconciler.updateServerPhase(serverStatus, app)
// 	app.Status.ServerStates = serverStatus
// 	err := reconciler.updateAppStatus(app)
// 	if err != nil && !errors.IsNotFound(err) {
// 		t.Errorf("Unexpected error: %v", err)
// 	}

// 	rsct := &appv1alpha1.ResourceCost{}
// 	if err := reconciler.Get(ctx, types.NamespacedName{Name: "app", Namespace: "app"}, rsct); err != nil {
// 		t.Errorf("Unexpected error: %v", err)
// 	}
// 	pods.Items[0].DeletionTimestamp = &metav1.Time{Time: time.Now()}
// 	status := reconciler.statPodHistorys(rsct, pods, app)
// 	rsct.Status = *status
// 	err = reconciler.Status().Update(ctx, rsct)
// 	if err != nil && !errors.IsNotFound(err) {
// 		t.Errorf("Unexpected error: %v", err)
// 	}
// 	cost := reconciler.statCurrentPods(rsct, pods, app)
// 	err = reconciler.Update(ctx, cost)
// 	if err != nil && !errors.IsNotFound(err) {
// 		t.Errorf("Unexpected error: %v", err)
// 	}
// }
