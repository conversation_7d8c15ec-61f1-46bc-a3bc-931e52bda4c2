package controller

// func Test_EventRecord(t *testing.T) {
// 	var scheme = runtime.NewScheme()
// 	utilruntime.Must(clientgoscheme.AddToScheme(scheme))
// 	utilruntime.Must(systemv1alpha1.AddToScheme(scheme))
// 	app := constructionApp("testing-app")
// 	obj := []client.Object{
// 		app,
// 	}
// 	c := fake.NewClientBuilder().WithScheme(scheme).WithObjects(obj...).Build()
// 	env := NewEventRecord(c, scheme)
// 	err := env.eventRecord(context.Background(), app, corev1.EventTypeWarning, "Failed", FailedAppMsg)
// 	if err != nil {
// 		t.<PERSON><PERSON>("Unexpected error: %v", err)
// 	}
// }
