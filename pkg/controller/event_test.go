package controller

import (
	"context"
	"testing"

	systemv1alpha1 "gitlab.leinao.ai/application-controller/apis/application/v1alpha1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/runtime"
	utilruntime "k8s.io/apimachinery/pkg/util/runtime"
	clientgoscheme "k8s.io/client-go/kubernetes/scheme"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"
)

func Test_EventRecord(t *testing.T) {
	var scheme = runtime.NewScheme()
	utilruntime.Must(clientgoscheme.AddToScheme(scheme))
	utilruntime.Must(systemv1alpha1.AddToScheme(scheme))
	app := constructionApp("testing-app")
	obj := []client.Object{
		app,
	}
	c := fake.NewClientBuilder().WithScheme(scheme).WithObjects(obj...).Build()
	env := NewEventRecord(c, scheme)
	err := env.eventRecord(context.Background(), app, corev1.EventTypeWarning, "Failed", FailedAppMsg)
	if err != nil {
		t.Errorf("Unexpected error: %v", err)
	}
}
