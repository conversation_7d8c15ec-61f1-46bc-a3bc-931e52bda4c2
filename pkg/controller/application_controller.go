/*
Copyright 2023.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package controller

import (
	"context"
	"fmt"
	"sync"

	jsoniter "github.com/json-iterator/go"
	"gitlab.leinao.ai/application-controller/pkg/backend/api"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"
	"sigs.k8s.io/controller-runtime/pkg/reconcile"

	"github.com/go-logr/logr"
	corev1 "k8s.io/api/core/v1"
	k8serrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/klog"
	ctrl "sigs.k8s.io/controller-runtime"

	systemv1alpha1 "gitlab.leinao.ai/application-controller/apis/application/v1alpha1"

	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/log"
)

const (
	controllerName = "application-controller"
	finalizerName  = "application.leinao.ai/finalizer"
	LabelUserIDKey = "leinao.ai/user-id"

	CreatingAppMsg      = "Creating Application"
	RestaringAppMsg     = "Restarting Application"
	RunningAppMsg       = "Application Running"
	StoppingAppMsg      = "Stopping Application"
	ListeningAppMsg     = "Application Listening"
	FailedAppMsg        = "Application Failed"
	AnnotationActionKey = "application.leinao.ai/action"
	ActionStopValue     = "stop"
	ActionStartValue    = "start"
	ActionUpdateValue   = "update"
)

// ApplicationReconciler reconciles a Application object
type ApplicationReconciler struct {
	client.Client
	Log                  logr.Logger
	Scheme               *runtime.Scheme
	EventRecord          *EventRecord
	CustomEventMapRecord sync.Map
	PrometheusAddress    string
	HostSuffix           string
}

//+kubebuilder:rbac:groups=system.hero.ai,resources=applications,verbs=get;list;watch;create;update;patch;delete
//+kubebuilder:rbac:groups=system.hero.ai,resources=applications/status,verbs=get;update;patch
//+kubebuilder:rbac:groups=system.hero.ai,resources=applications/finalizers,verbs=update

// Reconcile is part of the main kubernetes reconciliation loop which aims to
// move the current state of the cluster closer to the desired state.
// TODO(user): Modify the Reconcile function to compare the state specified by
// the Application object against the actual cluster state, and then
// perform operations to make the cluster state reflect the state specified by
// the user.
//
// For more details, check Reconcile and its Result here:
// - https://pkg.go.dev/sigs.k8s.io/controller-runtime@v0.16.0/pkg/reconcile
func (r *ApplicationReconciler) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) {
	_ = log.FromContext(ctx)
	_ = r.Log.WithValues("application", req.NamespacedName)
	app := &systemv1alpha1.Application{}
	if err := r.Get(ctx, req.NamespacedName, app); err != nil {
		return ctrl.Result{}, client.IgnoreNotFound(err)
	}

	done, result, err := r.finalize(ctx, app)
	if done {
		return result, err
	}
	totalServers := r.calculateTotalServers(app)

	// 有一个服务超过了最大重试次数或最长等待时间就失败
	if r.isApplicationFailed(app) {
		result, err := r.handleFailedApplication(ctx, app)
		if err != nil {
			return result, err
		}
	}

	done, result, err = r.processApplicationPhase(ctx, app, totalServers)
	if done {
		return result, err
	}

	//if app.Status.Phase == "" {
	//	// Create application
	//	result, err := r.processCreatState(ctx, app)
	//	if err != nil {
	//		return result, err
	//	}
	//} else if app.Status.Phase == systemv1alpha1.Creating {
	//
	//	if v, found := app.Annotations[AnnotationActionKey]; found && v == ActionStopValue {
	//		return r.stopApplication(ctx, app)
	//	}
	//	// serverless listening
	//	if app.Status.ListeningServers == int32(len(app.Spec.Server)) {
	//		app.Status.Phase = systemv1alpha1.Listening
	//		r.EventRecord.eventRecord(ctx, app, corev1.EventTypeNormal, "Listening", ListeningAppMsg)
	//		r.updateApplicationCondition(app, systemv1alpha1.ApplicationProgressing, corev1.ConditionTrue, "Listening", "The number of all server replicas is zero")
	//		err := r.updateAppStatus(ctx, app)
	//		if err != nil {
	//			return r.errorHandler(err)
	//		}
	//		return ctrl.Result{}, nil
	//	}
	//	if app.Status.AvailableServers == int32(totalServers) {
	//		app.Status.Phase = systemv1alpha1.Running
	//		r.EventRecord.eventRecord(ctx, app, corev1.EventTypeNormal, "Running", RunningAppMsg)
	//		r.updateApplicationCondition(app, systemv1alpha1.ApplicationAvailable, corev1.ConditionTrue, "Running", "Application is running")
	//		err := r.updateAppStatus(ctx, app)
	//		if err != nil {
	//			return r.errorHandler(err)
	//		}
	//	}
	//
	//	// TODO: Is it possible to stop the server when serverless is enabled?
	//} else if app.Status.Phase == systemv1alpha1.Running {
	//	// stop application
	//	if v, found := app.Annotations[AnnotationActionKey]; found && v == ActionStopValue {
	//		return r.stopApplication(ctx, app)
	//	}
	//	// serverless listening
	//	if app.Status.ListeningServers == int32(len(app.Spec.Server)) {
	//		app.Status.Phase = systemv1alpha1.Listening
	//		r.EventRecord.eventRecord(ctx, app, corev1.EventTypeNormal, "Listening", ListeningAppMsg)
	//		r.updateApplicationCondition(app, systemv1alpha1.ApplicationProgressing, corev1.ConditionTrue, "Listening", "The number of all server replicas is zero")
	//		err := r.updateAppStatus(ctx, app)
	//		if err != nil {
	//			return r.errorHandler(err)
	//		}
	//		return ctrl.Result{}, nil
	//	}
	//} else if app.Status.Phase == systemv1alpha1.Listening {
	//	// stop application
	//	if v, found := app.Annotations[AnnotationActionKey]; found && v == ActionStopValue {
	//		return r.stopApplication(ctx, app)
	//	}
	//	// 当AvailableServers等于app下server总数时，表示所有server都已经启动完成
	//	if app.Status.AvailableServers == int32(totalServers) {
	//		app.Status.Phase = systemv1alpha1.Running
	//		r.EventRecord.eventRecord(ctx, app, corev1.EventTypeNormal, "Running", RunningAppMsg)
	//		r.updateApplicationCondition(app, systemv1alpha1.ApplicationAvailable, corev1.ConditionTrue, "Running", "Application Listening AvailableServers greater than  zero")
	//		err := r.updateAppStatus(ctx, app)
	//		if err != nil {
	//			return r.errorHandler(err)
	//		}
	//	}
	//
	//} else if app.Status.Phase == systemv1alpha1.Stopping {
	//	// update application stopped phase
	//	if app.Status.AvailableServers <= 0 {
	//		app.Status.Phase = systemv1alpha1.Stopped
	//		r.updateApplicationCondition(app, systemv1alpha1.ApplicationAvailable, corev1.ConditionTrue, "Stopped", "Application has stopped")
	//		r.EventRecord.eventRecord(ctx, app, corev1.EventTypeNormal, "Success", StoppingAppMsg)
	//		err := r.updateAppStatus(ctx, app)
	//		if err != nil {
	//			return r.errorHandler(err)
	//		}
	//	}
	//} else if app.Status.Phase == systemv1alpha1.Stopped {
	//	//停止时赋值一个serverstatus的notReady的默认值
	//	serverState := systemv1alpha1.ServerState{Phase: systemv1alpha1.NotReady}
	//	if app.Status.ServerStates == nil {
	//		app.Status.ServerStates = make(map[string]systemv1alpha1.ServerState)
	//		for serverName, servers := range app.Spec.Server {
	//			for _, server := range servers {
	//				//key为服务名-服务版本号
	//				statusKey := fmt.Sprintf("%s-%s", serverName, server.Version)
	//				app.Status.ServerStates[statusKey] = serverState
	//			}
	//		}
	//		err := r.updateAppStatus(ctx, app)
	//		if err != nil {
	//			return r.errorHandler(err)
	//		}
	//	}
	//	// restarting application
	//	if v, found := app.Annotations[AnnotationActionKey]; found && v == ActionStartValue {
	//		r.updateApplicationCondition(app, systemv1alpha1.ApplicationAvailable, corev1.ConditionTrue, "ReStarting", "Application ReStarting")
	//		r.EventRecord.eventRecord(ctx, app, corev1.EventTypeNormal, "ReStarting", RestaringAppMsg)
	//		if err := r.CreateOrUpdateApp(ctx, app); err != nil {
	//			return r.errorHandler(err)
	//		}
	//		app.Status.Phase = systemv1alpha1.UnKnown
	//		err := r.updateAppStatus(ctx, app)
	//		if err != nil {
	//			return r.errorHandler(err)
	//		}
	//	}
	//} else if app.Status.Phase == systemv1alpha1.Failed {
	//	//解决每次重启推理服务失败事件都重复推送问题，需要看考虑失败后重启的情况
	//	if !conditionExists(app, systemv1alpha1.ApplicationReplicaFailure, corev1.ConditionTrue) {
	//		if _, found := app.Annotations[AnnotationActionKey]; !found {
	//			r.EventRecord.eventRecord(ctx, app, corev1.EventTypeWarning, "Failed", FailedAppMsg)
	//		}
	//	}
	//	r.updateApplicationCondition(app, systemv1alpha1.ApplicationReplicaFailure, corev1.ConditionTrue, "Failed", "Application Failed")
	//	//停止时赋值一个serverstatus的notReady的默认值,解决serverstatus偶发为空的bug
	//	serverState := systemv1alpha1.ServerState{Phase: systemv1alpha1.NotReady}
	//	if app.Status.ServerStates == nil {
	//		app.Status.ServerStates = make(map[string]systemv1alpha1.ServerState)
	//		for serverName, servers := range app.Spec.Server {
	//			for _, server := range servers {
	//				//key为服务名-服务版本号
	//				statusKey := fmt.Sprintf("%s-%s", serverName, server.Version)
	//				app.Status.ServerStates[statusKey] = serverState
	//				klog.Infof("the app.Status.ServerStatus is %+v", app.Status.ServerStates)
	//			}
	//		}
	//		err := r.updateAppStatus(ctx, app)
	//		if err != nil {
	//			return r.errorHandler(err)
	//		}
	//	}
	//	err := r.StopApp(ctx, app)
	//	if err != nil {
	//		return r.errorHandler(err)
	//	}
	//	// when app failed, restarting application
	//	if v, found := app.Annotations[AnnotationActionKey]; found && v == ActionStartValue {
	//		r.updateApplicationCondition(app, systemv1alpha1.ApplicationAvailable, corev1.ConditionTrue, "ReStarting", "Application ReStarting")
	//		r.EventRecord.eventRecord(ctx, app, corev1.EventTypeNormal, "ReStarting", RestaringAppMsg)
	//		if err := r.CreateOrUpdateApp(ctx, app); err != nil {
	//			return r.errorHandler(err)
	//		}
	//		app.Status.Phase = systemv1alpha1.UnKnown
	//		err := r.updateAppStatus(ctx, app)
	//		if err != nil {
	//			return r.errorHandler(err)
	//		}
	//		//如果包含启动的lable先执行一次启动 然后再去除这个annotation
	//		delete(app.Annotations, AnnotationActionKey)
	//		// Update the application to persist changes to annotations
	//		if err := r.Update(ctx, app); err != nil {
	//			return r.errorHandler(err)
	//		}
	//	}
	//	// 清理完成，直接退出
	//	return ctrl.Result{}, nil
	//}
	return ctrl.Result{}, nil
}

func (r *ApplicationReconciler) processApplicationPhase(ctx context.Context, app *systemv1alpha1.Application, totalServers int) (bool, ctrl.Result, error) {
	switch app.Status.Phase {
	case "":
		// Create application
		result, err := r.processCreatState(ctx, app)
		if err != nil {
			return true, result, err
		}
	case systemv1alpha1.Creating:
		done, result, err := r.processCreatingState(ctx, app, totalServers)
		if done {
			return true, result, err
		}
	case systemv1alpha1.Running:
		done, result, err := r.processRunningState(ctx, app)
		if done {
			return true, result, err
		}
	case systemv1alpha1.Listening:
		done, result, err := r.processListeningState(ctx, app, totalServers)
		if done {
			return true, result, err
		}
	case systemv1alpha1.Stopping:
		done, result, err := r.processStoppingState(ctx, app)
		if done {
			return true, result, err
		}
	case systemv1alpha1.Stopped:
		done, result, err := r.processStoppedState(ctx, app)
		if done {
			return true, result, err
		}
	case systemv1alpha1.Failed:
		_, result, err := r.processFailedState(ctx, app)
		return true, result, err
	default:
		return true, ctrl.Result{}, nil
	}
	return false, ctrl.Result{}, nil
}

func (r *ApplicationReconciler) processStoppingState(ctx context.Context, app *systemv1alpha1.Application) (bool, ctrl.Result, error) {
	if app.Status.AvailableServers <= 0 {
		app.Status.Phase = systemv1alpha1.Stopped
		r.updateApplicationCondition(app, systemv1alpha1.ApplicationAvailable, corev1.ConditionTrue, "Stopped", "Application has stopped")
		r.EventRecord.eventRecord(ctx, app, corev1.EventTypeNormal, "Success", StoppingAppMsg)
		err := r.updateAppStatus(ctx, app)
		if err != nil {
			return r.handleError(err)
		}
	}
	return false, ctrl.Result{}, nil
}

// shouldStopApplication 检查是否需要停止应用程序
func (r *ApplicationReconciler) shouldStopApplication(app *systemv1alpha1.Application) bool {
	v, found := app.Annotations[AnnotationActionKey]
	return found && v == ActionStopValue
}
func (r *ApplicationReconciler) processFailedState(ctx context.Context, app *systemv1alpha1.Application) (bool, ctrl.Result, error) {
	//解决每次重启推理服务失败事件都重复推送问题，需要看考虑失败后重启的情况
	if !conditionExists(app, systemv1alpha1.ApplicationReplicaFailure, corev1.ConditionTrue) {
		if _, found := app.Annotations[AnnotationActionKey]; !found {
			r.EventRecord.eventRecord(ctx, app, corev1.EventTypeWarning, "Failed", FailedAppMsg)
		}
	}
	r.updateApplicationCondition(app, systemv1alpha1.ApplicationReplicaFailure, corev1.ConditionTrue, "Failed", "Application Failed")
	//停止时赋值一个serverstatus的notReady的默认值,解决serverstatus偶发为空的bug
	serverState := systemv1alpha1.ServerState{Phase: systemv1alpha1.NotReady}
	if app.Status.ServerStates == nil {
		app.Status.ServerStates = make(map[string]systemv1alpha1.ServerState)
		for serverName, servers := range app.Spec.Server {
			for _, server := range servers {
				//key为服务名-服务版本号
				statusKey := fmt.Sprintf("%s-%s", serverName, server.Version)
				app.Status.ServerStates[statusKey] = serverState
				klog.Infof("the app.Status.ServerStatus is %+v", app.Status.ServerStates)
			}
		}
		err := r.updateAppStatus(ctx, app)
		if err != nil {
			return r.handleError(err)
		}
	}
	err := r.StopApp(ctx, app)
	if err != nil {
		return r.handleError(err)
	}
	// when app failed, restarting application
	if v, found := app.Annotations[AnnotationActionKey]; found && v == ActionStartValue {
		r.updateApplicationCondition(app, systemv1alpha1.ApplicationAvailable, corev1.ConditionTrue, "ReStarting", "Application ReStarting")
		r.EventRecord.eventRecord(ctx, app, corev1.EventTypeNormal, "ReStarting", RestaringAppMsg)
		if err := r.CreateOrUpdateApp(ctx, app); err != nil {
			return r.handleError(err)
		}
		app.Status.Phase = systemv1alpha1.UnKnown
		err := r.updateAppStatus(ctx, app)
		if err != nil {
			return r.handleError(err)
		}
		//如果包含启动的lable先执行一次启动 然后再去除这个annotation
		delete(app.Annotations, AnnotationActionKey)
		// Update the application to persist changes to annotations
		if err := r.Update(ctx, app); err != nil {
			return r.handleError(err)
		}
	}
	// 清理完成，直接退出
	return true, ctrl.Result{}, nil
}

func (r *ApplicationReconciler) processStoppedState(ctx context.Context, app *systemv1alpha1.Application) (bool, ctrl.Result, error) {
	//停止时赋值一个serverstatus的notReady的默认值
	serverState := systemv1alpha1.ServerState{Phase: systemv1alpha1.NotReady}
	if app.Status.ServerStates == nil {
		app.Status.ServerStates = make(map[string]systemv1alpha1.ServerState)
		for serverName, servers := range app.Spec.Server {
			for _, server := range servers {
				//key为服务名-服务版本号
				statusKey := fmt.Sprintf("%s-%s", serverName, server.Version)
				app.Status.ServerStates[statusKey] = serverState
			}
		}
		err := r.updateAppStatus(ctx, app)
		if err != nil {
			return r.handleError(err)
		}
	}
	// restarting application
	if v, found := app.Annotations[AnnotationActionKey]; found && v == ActionStartValue {
		r.updateApplicationCondition(app, systemv1alpha1.ApplicationAvailable, corev1.ConditionTrue, "ReStarting", "Application ReStarting")
		r.EventRecord.eventRecord(ctx, app, corev1.EventTypeNormal, "ReStarting", RestaringAppMsg)
		if err := r.CreateOrUpdateApp(ctx, app); err != nil {
			return r.handleError(err)
		}
		app.Status.Phase = systemv1alpha1.UnKnown
		err := r.updateAppStatus(ctx, app)
		if err != nil {
			return r.handleError(err)
		}
	}
	return false, ctrl.Result{}, nil
}

func (r *ApplicationReconciler) processListeningState(ctx context.Context, app *systemv1alpha1.Application, totalServers int) (bool, ctrl.Result, error) {
	// stop application
	if r.shouldStopApplication(app) {
		return r.stopApplication(ctx, app)
	}

	// 当AvailableServers等于app下server总数时，表示所有server都已经启动完成
	if app.Status.AvailableServers == int32(totalServers) {
		app.Status.Phase = systemv1alpha1.Running
		r.EventRecord.eventRecord(ctx, app, corev1.EventTypeNormal, "Running", RunningAppMsg)
		r.updateApplicationCondition(app, systemv1alpha1.ApplicationAvailable, corev1.ConditionTrue, "Running", "Application Listening AvailableServers greater than  zero")
		err := r.updateAppStatus(ctx, app)
		if err != nil {
			return r.handleError(err)
		}
	}
	return false, ctrl.Result{}, nil
}

func (r *ApplicationReconciler) processRunningState(ctx context.Context, app *systemv1alpha1.Application) (bool, ctrl.Result, error) {
	if r.shouldStopApplication(app) {
		return r.stopApplication(ctx, app)
	}

	if app.Status.ListeningServers == int32(len(app.Spec.Server)) {
		app.Status.Phase = systemv1alpha1.Listening
		r.EventRecord.eventRecord(ctx, app, corev1.EventTypeNormal, "Listening", ListeningAppMsg)
		r.updateApplicationCondition(app, systemv1alpha1.ApplicationProgressing, corev1.ConditionTrue, "Listening", "The number of all server replicas is zero")
		err := r.updateAppStatus(ctx, app)
		if err != nil {
			return r.handleError(err)
		}
		return true, ctrl.Result{}, nil
	}
	return false, ctrl.Result{}, nil
}

func (r *ApplicationReconciler) processCreatingState(ctx context.Context, app *systemv1alpha1.Application, totalServers int) (bool, ctrl.Result, error) {
	if r.shouldStopApplication(app) {
		return r.stopApplication(ctx, app)
	}
	// serverless listening
	if app.Status.ListeningServers == int32(len(app.Spec.Server)) {
		app.Status.Phase = systemv1alpha1.Listening
		r.EventRecord.eventRecord(ctx, app, corev1.EventTypeNormal, "Listening", ListeningAppMsg)
		r.updateApplicationCondition(app, systemv1alpha1.ApplicationProgressing, corev1.ConditionTrue, "Listening", "The number of all server replicas is zero")
		err := r.updateAppStatus(ctx, app)
		if err != nil {
			return r.handleError(err)
		}
		return true, ctrl.Result{}, nil
	}
	if app.Status.AvailableServers == int32(totalServers) {
		app.Status.Phase = systemv1alpha1.Running
		r.EventRecord.eventRecord(ctx, app, corev1.EventTypeNormal, "Running", RunningAppMsg)
		r.updateApplicationCondition(app, systemv1alpha1.ApplicationAvailable, corev1.ConditionTrue, "Running", "Application is running")
		err := r.updateAppStatus(ctx, app)
		if err != nil {
			return r.handleError(err)
		}
	}
	return false, ctrl.Result{}, nil
}

func (r *ApplicationReconciler) processCreatState(ctx context.Context, app *systemv1alpha1.Application) (ctrl.Result, error) {
	r.updateApplicationCondition(app, systemv1alpha1.ApplicationProgressing, corev1.ConditionTrue, "Creating", "Application is being created")
	r.EventRecord.eventRecord(ctx, app, corev1.EventTypeNormal, "Creating", CreatingAppMsg)
	if err := r.CreateOrUpdateApp(ctx, app); err != nil {
		app.Status.Phase = systemv1alpha1.Failed
		err := r.updateAppStatus(ctx, app)
		if err != nil {
			return r.errorHandler(err)
		}
	}
	app.Status.Phase = systemv1alpha1.Creating
	err := r.updateAppStatus(ctx, app)
	if err != nil {
		return r.errorHandler(err)
	}
	r.EventRecord.eventRecord(ctx, app, corev1.EventTypeNormal, "Success", CreatingAppMsg)
	return ctrl.Result{}, nil
}

func (r *ApplicationReconciler) finalize(ctx context.Context, app *systemv1alpha1.Application) (bool, ctrl.Result, error) {
	if app.ObjectMeta.DeletionTimestamp.IsZero() {
		if !controllerutil.ContainsFinalizer(app, finalizerName) {
			controllerutil.AddFinalizer(app, finalizerName)
			if err := r.Update(ctx, app); err != nil {
				return r.handleError(err)
			}
		}
	} else {
		// The application is being deleted
		if controllerutil.ContainsFinalizer(app, finalizerName) {
			app.Status.Phase = systemv1alpha1.Terminating
			err := r.updateAppStatus(ctx, app)
			if err != nil {
				return r.handleError(err)
			}
			controllerutil.RemoveFinalizer(app, finalizerName)
			err = r.Update(ctx, app)
			if err != nil {
				return r.handleError(err)
			}
		}
		r.CustomEventMapRecord.Delete(app.Name)
		return true, ctrl.Result{}, nil
	}
	return false, ctrl.Result{}, nil
}

// handleError 统一处理错误
func (r *ApplicationReconciler) handleError(err error) (bool, ctrl.Result, error) {
	result, err := r.errorHandler(err)
	return true, result, err
}

func (r *ApplicationReconciler) calculateTotalServers(app *systemv1alpha1.Application) int {
	totalServers := 0
	for _, server := range app.Spec.Server {
		totalServers += len(server)
	}
	return totalServers
}

func (r *ApplicationReconciler) isApplicationFailed(app *systemv1alpha1.Application) bool {
	return app.Status.FailedServers > 0 && app.Status.Phase != systemv1alpha1.Failed
}

func (r *ApplicationReconciler) handleFailedApplication(ctx context.Context, app *systemv1alpha1.Application) (ctrl.Result, error) {
	app.Status.Phase = systemv1alpha1.Failed
	r.updateApplicationCondition(app, systemv1alpha1.ApplicationReplicaFailure, corev1.ConditionTrue, "Failed", "Application Failed")
	if err := r.updateAppStatus(ctx, app); err != nil {
		return r.errorHandler(err)
	}
	return ctrl.Result{}, nil
}

func (r *ApplicationReconciler) updateAppStatus(ctx context.Context, app *systemv1alpha1.Application) error {
	r.Log.Info("updateAppStatus", "app", app.Name, "status", app.Status.Phase)
	if e := r.Status().Update(ctx, app); e != nil {
		return e
	}
	return nil
}

func getAllBackends(app *systemv1alpha1.Application) []api.BackendInterface {
	gateway := systemv1alpha1.GatewayBackend("")
	serviceMesh := systemv1alpha1.ServiceMeshBackend("")

	autoScaling := systemv1alpha1.AutoScalingBackend("")
	autoScaling = getAutoscalingBackend(app, autoScaling)

	if app.Spec.Gateway != nil {
		gateway = app.Spec.Gateway.Backend
	}
	if app.Spec.ServiceMesh != nil && app.Spec.ServiceMesh.Backend != "" {
		serviceMesh = app.Spec.ServiceMesh.Backend
	}
	if serviceMesh == "" {
		for _, server := range app.Spec.Server {
			for _, ser := range server {
				if ser.ServiceMesh != nil && ser.ServiceMesh.Backend != "" {
					serviceMesh = ser.ServiceMesh.Backend
					goto Loop
				}
			}
		}
	}

Loop:
	return []api.BackendInterface{
		api.GetServiceMeshBackend(serviceMesh),
		api.GetGatewayBackend(gateway),
		api.GetSchedulerBackend(app.Spec.Scheduler.SchedulerName),
		api.GetDeployBackend(),
		api.GetServiceBackend(),
		api.GetAutoScalingBackend(autoScaling),
		api.GetResourceCostBackend(),
	}
}

func getAutoscalingBackend(app *systemv1alpha1.Application, autoScaling systemv1alpha1.AutoScalingBackend) systemv1alpha1.AutoScalingBackend {
	for _, servers := range app.Spec.Server {
		for _, server := range servers {
			if app.Spec.AutoScaling != nil && app.Spec.AutoScaling.Backend != "" && server.AutoScaling != nil && (server.AutoScaling.Triggers != nil || server.AutoScaling.Serverless != nil) {
				autoScaling = app.Spec.AutoScaling.Backend
			}
		}
	}
	return autoScaling
}

func (r *ApplicationReconciler) StopApp(ctx context.Context, app *systemv1alpha1.Application) error {

	backends := getAllBackends(app)
	for _, backend := range backends {
		r.Log.Info("Cleaning", "backend", backend.Name(), "app", app.Name)
		err := backend.Clean(app)
		if err != nil {
			r.Log.Error(err, "Clean failed")
			//return err
		}
	}
	return nil
}

func (r *ApplicationReconciler) CreateOrUpdateApp(ctx context.Context, app *systemv1alpha1.Application) error {
	backends := getAllBackends(app)
	for _, backend := range backends {
		err := backend.Inject(app)
		if err != nil {
			r.Log.Error(err, "Inject failed")
		}
	}

	for _, backend := range backends {
		err := backend.Configure(app)
		if err != nil {
			r.Log.Error(err, "Configure failed")
		}
	}

	if value, found := app.Annotations[AnnotationActionKey]; found && value == ActionStartValue {
		delete(app.Annotations, AnnotationActionKey)
		if err := r.Update(ctx, app); err != nil {
			return err
		}
	}
	return nil
}

func (r *ApplicationReconciler) errorHandler(err error) (ctrl.Result, error) {
	if k8serrors.IsConflict(err) {
		return reconcile.Result{Requeue: true}, nil
	}
	return ctrl.Result{}, err
}

// SetupWithManager sets up the controller with the Manager.
func (r *ApplicationReconciler) SetupWithManager(mgr ctrl.Manager) error {
	if r.Log.GetSink() == nil {
		r.Log = ctrl.Log.WithName("controllers").WithName(controllerName)
	}
	api.SetupAllBackends(r.Client, r.Scheme, r.PrometheusAddress, r.HostSuffix)

	// 添加索引
	indexer := mgr.GetFieldIndexer()

	// 为 involvedObject.name 设置索引
	if err := indexer.IndexField(context.Background(), &corev1.Event{}, "involvedObject.name", func(o client.Object) []string {
		return []string{o.(*corev1.Event).InvolvedObject.Name}
	}); err != nil {
		return err
	}

	// 为 involvedObject.kind 设置索引
	if err := indexer.IndexField(context.Background(), &corev1.Event{}, "involvedObject.kind", func(o client.Object) []string {
		return []string{o.(*corev1.Event).InvolvedObject.Kind}
	}); err != nil {
		return err
	}

	// 为 involvedObject.apiVersion 设置索引
	if err := indexer.IndexField(context.Background(), &corev1.Event{}, "involvedObject.apiVersion", func(o client.Object) []string {
		return []string{o.(*corev1.Event).InvolvedObject.APIVersion}
	}); err != nil {
		return err
	}

	return ctrl.NewControllerManagedBy(mgr).
		For(&systemv1alpha1.Application{}).
		Complete(r)
}

func (r *ApplicationReconciler) DeployConfigMap(app *systemv1alpha1.Application) *corev1.ConfigMap {
	data, _ := jsoniter.MarshalToString(app.Spec)
	cm := &corev1.ConfigMap{
		ObjectMeta: metav1.ObjectMeta{
			Name:      app.Name,
			Namespace: app.Namespace,
		},
		Data: map[string]string{
			app.Spec.Version: data,
		},
	}
	cm.SetOwnerReferences(nil)
	_ = controllerutil.SetControllerReference(app, cm, r.Scheme)
	return cm
}

func (r *ApplicationReconciler) updateApplicationCondition(app *systemv1alpha1.Application, conditionType systemv1alpha1.ApplicationConditionType, status corev1.ConditionStatus, reason, message string) {
	now := metav1.Now()
	var condition *systemv1alpha1.ApplicationCondition
	for i, cond := range app.Status.Conditions {
		if cond.Type == conditionType {
			condition = &app.Status.Conditions[i]
			break
		}
	}

	if condition == nil {
		condition = &systemv1alpha1.ApplicationCondition{
			Type:               conditionType,
			Status:             status,
			LastTransitionTime: now,
			LastUpdateTime:     now,
			Reason:             reason,
			Message:            message,
		}
		app.Status.Conditions = append(app.Status.Conditions, *condition)
		return
	}

	if condition.Status != status {
		condition.Status = status
		condition.LastTransitionTime = now
	}
	condition.LastUpdateTime = now
	condition.Reason = reason
	condition.Message = message
}

func conditionExists(app *systemv1alpha1.Application, conditionType systemv1alpha1.ApplicationConditionType, status corev1.ConditionStatus) bool {
	for _, cond := range app.Status.Conditions {
		if cond.Type == conditionType && cond.Status == status {
			return true
		}
	}
	return false
}

func (r *ApplicationReconciler) stopApplication(ctx context.Context, app *systemv1alpha1.Application) (bool, ctrl.Result, error) {
	r.EventRecord.eventRecord(ctx, app, corev1.EventTypeNormal, "Stopping", StoppingAppMsg)
	err := r.StopApp(ctx, app)
	if err != nil {
		return r.handleError(err)
	}
	app.Status.Phase = systemv1alpha1.Stopping
	r.updateApplicationCondition(app, systemv1alpha1.ApplicationProgressing, corev1.ConditionTrue, "Stopping", StoppingAppMsg)
	err = r.updateAppStatus(ctx, app)
	if err != nil {
		return r.handleError(err)
	}
	return false, ctrl.Result{}, nil
}
