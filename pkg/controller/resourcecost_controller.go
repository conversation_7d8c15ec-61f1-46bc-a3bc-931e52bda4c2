/*
Copyright 2023.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package controller

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/go-logr/logr"
	corev1 "k8s.io/api/core/v1"
	k8serrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/tools/record"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/builder"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/handler"
	"sigs.k8s.io/controller-runtime/pkg/log"
	"sigs.k8s.io/controller-runtime/pkg/predicate"
	"sigs.k8s.io/controller-runtime/pkg/reconcile"

	systemv1alpha1 "gitlab.leinao.ai/application-controller/apis/application/v1alpha1"
	"k8s.io/kubernetes/pkg/util/node"
)

//var (
//	apiGVStr = systemv1alpha1.GroupVersion.String()
//)

const (
	PodConditionReady = "Ready"
)

// ResourceCostReconciler reconciles a Application object
type ResourceCostReconciler struct {
	client.Client
	Log      logr.Logger
	Scheme   *runtime.Scheme
	Recorder record.EventRecorder
}

//+kubebuilder:rbac:groups=system.hero.ai,resources=resourcecosts,verbs=get;list;watch;create;update;patch;delete
//+kubebuilder:rbac:groups=system.hero.ai,resources=pods,verbs=get;list;watch
//+kubebuilder:rbac:groups=system.hero.ai,resources=resourcecosts/status,verbs=get;update;patch
//+kubebuilder:rbac:groups=system.hero.ai,resources=resourcecosts/finalizers,verbs=update

// Reconcile is part of the main kubernetes reconciliation loop which aims to
// move the current state of the cluster closer to the desired state.
// TODO(user): Modify the Reconcile function to compare the state specified by
// the Application object against the actual cluster state, and then
// perform operations to make the cluster state reflect the state specified by
// the user.
//
// For more details, check Reconcile and its Result here:
// - https://pkg.go.dev/sigs.k8s.io/controller-runtime@v0.16.0/pkg/reconcile
func (r *ResourceCostReconciler) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) {
	_ = log.FromContext(ctx)
	_ = r.Log.WithValues("resource-cost", req.NamespacedName)

	app := &systemv1alpha1.Application{}
	if err := r.Get(ctx, req.NamespacedName, app); err != nil {
		return ctrl.Result{}, client.IgnoreNotFound(err)
	}
	pods := &corev1.PodList{}
	if err := r.List(ctx, pods, client.InNamespace(req.Namespace), client.MatchingFields{systemv1alpha1.AppIndexField: req.Name}); err != nil {
		r.Log.Error(err, "unable to list pods of Application")
		return ctrl.Result{}, err
	}
	serverStatus := r.buildServerStatus(pods, app)
	r.updateServerPhase(serverStatus, app)
	app.Status.ServerStates = serverStatus
	err := r.updateAppStatus(app)
	if err != nil {
		return r.errorHandler(err)
	}

	var result ctrl.Result
	if app.Status.Phase == systemv1alpha1.Creating && app.Spec.Scheduler.MaxPendingDuration != nil {
		result = ctrl.Result{
			Requeue:      true,
			RequeueAfter: *app.Spec.Scheduler.MaxPendingDuration,
		}
	}

	// TODO Calc Application ResourceCost
	rsct := &systemv1alpha1.ResourceCost{}
	if err := r.Get(ctx, req.NamespacedName, rsct); err != nil {
		return ctrl.Result{}, client.IgnoreNotFound(err)
	}
	status := r.statPodHistorys(rsct, pods, app)
	rsct.Status = *status
	err = r.Status().Update(ctx, rsct)
	if err != nil {
		r.Log.Error(err, "update resource cost status error")
		return r.errorHandler(err)
	}
	cost := r.statCurrentPods(rsct, pods, app)
	err = r.Update(ctx, cost)
	if err != nil {
		return r.errorHandler(err)
	}
	return result, nil
}

func (r *ResourceCostReconciler) statCurrentPods(rsct *systemv1alpha1.ResourceCost, pods *corev1.PodList, app *systemv1alpha1.Application) *systemv1alpha1.ResourceCost {
	rsct.Spec.Current.Pods = make(map[string][]*systemv1alpha1.PodInfo)
	for _, pod := range pods.Items {
		if pod.Status.Phase == corev1.PodRunning || pod.Status.Phase == corev1.PodPending {
			serverName := pod.Labels[systemv1alpha1.LabelAppName]
			serverVersion := pod.Labels[systemv1alpha1.LabelAppversion]
			appName := pod.Labels[systemv1alpha1.LabelApplicationName]
			appVersion := pod.Labels[systemv1alpha1.LabelApplicationVersion]
			appJobID := app.GetAnnotations()[systemv1alpha1.LabelApplicationJobID]
			var podKey string
			if appJobID == "" {
				podKey = fmt.Sprintf("%s/%s/%s/%s", appName, appVersion, serverName, serverVersion)
			} else {
				podKey = fmt.Sprintf("%s/%s/%s/%s/%s", appName, appVersion, appJobID, serverName, serverVersion)
			}
			createTime := r.getPodCreateTime(&pod)
			runningTime := r.getPodRunningTime(&pod)
			completeTime := r.getPodCompleteTime(&pod)
			// current中只记录正在运行的pod，即pod的有停止时间的
			if !completeTime.IsZero() {
				continue
			}
			status, err := getPodContainerMainStatus(&pod)
			if err != nil {
				r.Log.Error(err, "get pod container main status error")
			}
			podInfo := &systemv1alpha1.PodInfo{
				Name:            pod.Name,
				ContainerName:   app.Name,
				Resource:        pod.Spec.Containers[0].Resources.Limits,
				CreatingTime:    &createTime,
				RunningTime:     &runningTime,
				StoppedTime:     &completeTime,
				ContainerStatus: status,
				NodeName:        pod.Spec.NodeName,
			}
			if rsct.Spec.Current.Pods[podKey] != nil {
				rsct.Spec.Current.Pods[podKey] = append(rsct.Spec.Current.Pods[podKey], podInfo)
			} else {
				rsct.Spec.Current.Pods[podKey] = []*systemv1alpha1.PodInfo{podInfo}
			}
		}
	}
	return rsct
}

func (r *ResourceCostReconciler) statPodHistorys(rsct *systemv1alpha1.ResourceCost, pods *corev1.PodList, app *systemv1alpha1.Application) *systemv1alpha1.ResourceCostStatus {
	if rsct.Status.Pods == nil {
		rsct.Status.Pods = make(map[string][]*systemv1alpha1.PodInfo)
	}
	for _, pod := range pods.Items {
		r.Log.Info("process pod status", "pod", pod.Name, "phase", pod.Status.Phase, "deletionTimestamp", pod.DeletionTimestamp)
		if (pod.Status.Phase == corev1.PodRunning && pod.DeletionTimestamp == nil) || (pod.Status.Phase == corev1.PodPending && pod.DeletionTimestamp == nil) {
			continue
		}
		r.Log.Info("add pod to history", "pod", pod.Name)
		serverName := pod.Labels[systemv1alpha1.LabelAppName]
		serverVersion := pod.Labels[systemv1alpha1.LabelAppversion]
		appName := pod.Labels[systemv1alpha1.LabelApplicationName]
		appVersion := pod.Labels[systemv1alpha1.LabelApplicationVersion]
		appJobID := app.GetAnnotations()[systemv1alpha1.LabelApplicationJobID]
		var podKey string
		if appJobID == "" {
			podKey = fmt.Sprintf("%s/%s/%s/%s", appName, appVersion, serverName, serverVersion)
		} else {
			podKey = fmt.Sprintf("%s/%s/%s/%s/%s", appName, appVersion, appJobID, serverName, serverVersion)
		}
		createTime := r.getPodCreateTime(&pod)
		runningTime := r.getPodRunningTime(&pod)
		completeTime := r.getPodCompleteTime(&pod)
		// status中只记录已经停止的pod，即pod的有停止时间的
		if completeTime.IsZero() {
			continue
		}
		status, err := getPodContainerMainStatus(&pod)
		if err != nil {
			r.Log.Error(err, "get pod container main status error")
		}

		appendPods := []*systemv1alpha1.PodInfo{
			{
				Name:            pod.Name,
				ContainerName:   app.Name,
				Resource:        pod.Spec.Containers[0].Resources.Limits,
				CreatingTime:    &createTime,
				RunningTime:     &runningTime,
				StoppedTime:     &completeTime,
				ContainerStatus: status,
				NodeName:        pod.Spec.NodeName,
			},
		}
		r.savePodstatusIntoRsctStatus(rsct, podKey, pod, createTime, runningTime, completeTime, app, serverName, serverVersion, appendPods)
		r.Log.Info("add pod to resource cost success", "pod", pod.Name, "phase", status, "CreatedTime", createTime, "RunningTime", runningTime, "StoppedTime", completeTime)
	}
	// ADD pod history clean, 时间超过2天的记录清理掉
	cleanPodHistory(&rsct.Status)
	return &rsct.Status
}

func (r *ResourceCostReconciler) savePodstatusIntoRsctStatus(rsct *systemv1alpha1.ResourceCost, podKey string, pod corev1.Pod, createTime metav1.Time, runningTime metav1.Time, completeTime metav1.Time, app *systemv1alpha1.Application, serverName string, serverVersion string, appendPods []*systemv1alpha1.PodInfo) {
	if podInfos, found := rsct.Status.Pods[podKey]; found {
		index := 0
		for _, podInfo := range podInfos {
			if podInfo.Name == pod.Name {
				podInfo.CreatingTime = &createTime
				// TODO 处理pod CrashLoopBackOff导致runningTime一直变化的情况
				if podInfo.RunningTime == nil {
					podInfo.RunningTime = &runningTime
				}
				// stoppedTime可能会变化，这里只赋值为第一次停止的时间
				if podInfo.StoppedTime == nil {
					podInfo.StoppedTime = &completeTime
				}
			} else {
				index++
			}
		}
		if index == len(podInfos) {
			podHistory := r.saveLatestPods(app, serverName, serverVersion, podInfos, appendPods[0])
			rsct.Status.Pods[podKey] = podHistory
		}
	} else {
		rsct.Status.Pods[podKey] = appendPods
	}
}

func (r *ResourceCostReconciler) saveLatestPods(app *systemv1alpha1.Application, serverName string, serverVersion string, podInfos []*systemv1alpha1.PodInfo, appendPod *systemv1alpha1.PodInfo) []*systemv1alpha1.PodInfo {
	var replicas int
	for _, server := range app.Spec.Server[serverName] {
		if server.Version == serverVersion {
			replicas = int(*server.Replicas)
		}
	}
	if serverStatus, found := app.Status.ServerStates[fmt.Sprintf("%s-%s", serverName, serverVersion)]; found {
		if replicas < int(serverStatus.Replicas) {
			replicas = int(serverStatus.Replicas)
		}
	}
	savedReplicas := replicas * 2

	// 确保 savedReplicas 不超过现有 podInfos 的长度加上一个 appendPod
	if savedReplicas > len(podInfos)+1 {
		savedReplicas = len(podInfos) + 1
	}

	var podHistory []*systemv1alpha1.PodInfo

	// 如果podInfos的长度超过savedReplicas减1，则截取前savedReplicas-1个元素
	if len(podInfos) >= savedReplicas-1 {
		podHistory = podInfos[:savedReplicas-1]
	} else {
		podHistory = make([]*systemv1alpha1.PodInfo, len(podInfos))
		copy(podHistory, podInfos)
	}

	// 添加 appendPod 到 podHistory 的开始
	podHistory = append([]*systemv1alpha1.PodInfo{appendPod}, podHistory...)

	r.Log.Info("update resource cost", "old pods length", len(podInfos), "save pods length", savedReplicas, "update podHistory length", len(podHistory))
	return podHistory
}

func (r *ResourceCostReconciler) getPodRunningTime(pod *corev1.Pod) metav1.Time {
	containerName := pod.Spec.Containers[0].Name
	for _, containerStatus := range pod.Status.ContainerStatuses {
		if containerStatus.Name == containerName && containerStatus.State.Running != nil {
			return containerStatus.State.Running.StartedAt
		}
		if containerStatus.Name == containerName && containerStatus.State.Terminated != nil {
			return containerStatus.State.Terminated.StartedAt
		}
	}
	for _, podCondition := range pod.Status.Conditions {
		if podCondition.Type == corev1.PodReady {
			return podCondition.LastTransitionTime
		}
	}

	return r.getPodCompleteTime(pod)
}

func (r *ResourceCostReconciler) getPodCompleteTime(pod *corev1.Pod) metav1.Time {
	containerName := pod.Spec.Containers[0].Name
	// TODO 删除时间戳优化
	for _, containerStatus := range pod.Status.ContainerStatuses {
		if containerStatus.Name == containerName && containerStatus.State.Terminated != nil {
			return containerStatus.State.Terminated.FinishedAt
		}
	}
	if pod.DeletionTimestamp != nil {
		return pod.DeletionTimestamp.Rfc3339Copy()
	}
	return metav1.Time{}
}

func (r *ResourceCostReconciler) getPodCreateTime(pod *corev1.Pod) metav1.Time {
	if !pod.CreationTimestamp.IsZero() {
		return pod.CreationTimestamp
	}
	// 如果 CreationTimestamp 为空，检查 StartTime 是否已设置
	if pod.Status.StartTime != nil {
		return pod.Status.StartTime.Rfc3339Copy()
	}
	//if pod.Status.StartTime != nil {
	//	if !pod.CreationTimestamp.IsZero() {
	//		return pod.CreationTimestamp
	//	} else {
	//		return pod.Status.StartTime.Rfc3339Copy()
	//	}
	//}
	return metav1.Time{}
}

func (r *ResourceCostReconciler) buildServerStatus(pods *corev1.PodList, app *systemv1alpha1.Application) map[string]systemv1alpha1.ServerState {

	servers := make(map[string]systemv1alpha1.ServerState)
	for _, pod := range pods.Items {
		serverName := pod.Labels[systemv1alpha1.LabelAppName] + "-" + pod.Labels[systemv1alpha1.LabelAppversion]
		var serverState systemv1alpha1.ServerState
		var found bool
		if serverState, found = servers[serverName]; !found {
			serverState = systemv1alpha1.ServerState{}
		}
		serverState.Replicas++
		if ser, found := app.Status.ServerStates[serverName]; found {
			serverState.RetryNum = ser.RetryNum
			serverState.PendingDuration = ser.PendingDuration
		}
		var podConditionStatus string
		for _, podCondition := range pod.Status.Conditions {
			if podCondition.Type == corev1.PodReady && podCondition.Status == corev1.ConditionTrue {
				podConditionStatus = PodConditionReady
			}
		}
		if len(pod.Status.InitContainerStatuses) > 0 {
			for _, initStatus := range pod.Status.InitContainerStatuses {
				if serverState.RetryNum < initStatus.RestartCount {
					serverState.RetryNum = initStatus.RestartCount
				}
			}
		}

		if len(pod.Status.ContainerStatuses) > 0 {
			if serverState.RetryNum < pod.Status.ContainerStatuses[0].RestartCount {
				serverState.RetryNum = pod.Status.ContainerStatuses[0].RestartCount
			}
		}
		if pod.Status.Phase == corev1.PodRunning && podConditionStatus == PodConditionReady {
			serverState.AvailableReplicas++
		} else if pod.Status.Phase == corev1.PodPending {
			// 多个pod如何选择pending时间，取pending时间最长的，有running的则为0
			if serverState.AvailableReplicas <= 0 {
				sub := metav1.Now().Sub(pod.CreationTimestamp.Time)
				if serverState.PendingDuration == nil || *serverState.PendingDuration < sub {
					serverState.PendingDuration = &sub
				}
			} else {
				serverState.PendingDuration = nil
			}
		}
		servers[serverName] = serverState
	}
	return servers
}

func (r *ResourceCostReconciler) updateAppStatus(app *systemv1alpha1.Application) error {
	app.Status.Replicas = 0
	app.Status.AvailableReplicas = 0
	app.Status.AvailableServers = 0
	app.Status.FailedServers = 0
	app.Status.ListeningServers = 0
	for _, state := range app.Status.ServerStates {
		app.Status.Replicas += state.Replicas
		app.Status.AvailableReplicas += state.AvailableReplicas
		// 当server下有至少一个副本可用时，认为该server为available
		if state.AvailableReplicas > 0 {
			app.Status.AvailableServers++
		}
		if state.Phase == systemv1alpha1.OutOfMaxRetry || state.Phase == systemv1alpha1.OutOfMaxPending {
			app.Status.FailedServers++
		}
		if state.Phase == systemv1alpha1.Paused {
			app.Status.ListeningServers++
		}
	}
	err := r.Status().Update(context.Background(), app)
	if err != nil {
		return err
	}
	return nil
}

func (r *ResourceCostReconciler) findPodsForApplication(ctx context.Context, object client.Object) []reconcile.Request {
	pod := object.(*corev1.Pod)
	var requests []reconcile.Request
	if pod.Labels[systemv1alpha1.LabelApplicationName] != "" {
		requests = []reconcile.Request{
			{
				NamespacedName: types.NamespacedName{
					Namespace: pod.Namespace,
					Name:      pod.Labels[systemv1alpha1.LabelApplicationName],
				},
			},
		}
	}
	return requests
}

func (r *ResourceCostReconciler) errorHandler(err error) (ctrl.Result, error) {
	if k8serrors.IsConflict(err) {
		return reconcile.Result{Requeue: true}, nil
	}
	return ctrl.Result{}, err
}

// SetupWithManager sets up the controller with the Manager.
func (r *ResourceCostReconciler) SetupWithManager(mgr ctrl.Manager) error {
	if r.Log.GetSink() == nil {
		r.Log = ctrl.Log.WithName("controllers").WithName(controllerName)
	}
	if r.Recorder == nil {
		r.Recorder = mgr.GetEventRecorderFor(controllerName)
	}
	if err := mgr.GetFieldIndexer().IndexField(context.Background(), &corev1.Pod{}, systemv1alpha1.AppIndexField, func(object client.Object) []string {
		pod := object.(*corev1.Pod)
		if appName, found := pod.Labels[systemv1alpha1.LabelApplicationName]; found {
			return []string{appName}
		}
		return nil
	}); err != nil {
		return err
	}
	return ctrl.NewControllerManagedBy(mgr).
		For(&systemv1alpha1.ResourceCost{}).
		Watches(
			&corev1.Pod{},
			handler.EnqueueRequestsFromMapFunc(r.findPodsForApplication),
			builder.WithPredicates(predicate.ResourceVersionChangedPredicate{}),
		).
		Complete(r)
}

func (r *ResourceCostReconciler) updateServerPhase(status map[string]systemv1alpha1.ServerState, app *systemv1alpha1.Application) {
	if app.Spec.AutoScaling != nil && app.Spec.AutoScaling.Serverless != nil {
		if len(status) == 0 {
			r.initListeningServerStatus(status, app)
			return
		}
	}
	r.updateServerStatus(status, app)
}

func (r *ResourceCostReconciler) updateServerStatus(status map[string]systemv1alpha1.ServerState, app *systemv1alpha1.Application) {
	for sname, state := range status {
		maxRetryNum := int32(0)
		maxPendingDuration := time.Duration(0)
		serverName := sname[:strings.LastIndex(sname, "-")]
		serverVersion := sname[strings.LastIndex(sname, "-")+1:]

		if serverSpecs, found := app.Spec.Server[serverName]; found {
			for _, serverSpec := range serverSpecs {
				if serverSpec.Version == serverVersion {
					maxRetryNum, maxPendingDuration = getSchedulerValues(&serverSpec.Scheduler, &app.Spec.Scheduler)
				}
			}
		}
		// enable hpa
		if app.Spec.AutoScaling != nil && app.Spec.AutoScaling.Serverless != nil {
			if state.AvailableReplicas == 0 && state.Replicas >= 0 {
				state.Phase = systemv1alpha1.Paused
			}
		}
		setServerPhase(&state, maxRetryNum, maxPendingDuration)

		status[sname] = state
	}
}

func getSchedulerValues(scheduler *systemv1alpha1.SchedulerSpec, defaultScheduler *systemv1alpha1.SchedulerSpec) (int32, time.Duration) {
	maxRetry := int32(0)
	maxPendingDuration := time.Duration(0)

	if scheduler.MaxRetry != nil {
		maxRetry = *scheduler.MaxRetry
	} else if defaultScheduler.MaxRetry != nil {
		maxRetry = *defaultScheduler.MaxRetry
	}

	if scheduler.MaxPendingDuration != nil {
		maxPendingDuration = *scheduler.MaxPendingDuration * time.Minute
	} else if defaultScheduler.MaxPendingDuration != nil {
		maxPendingDuration = *defaultScheduler.MaxPendingDuration * time.Minute
	}

	return maxRetry, maxPendingDuration
}

func setServerPhase(state *systemv1alpha1.ServerState, maxRetryNum int32, maxPendingDuration time.Duration) {
	if state.Phase == "" {
		if state.AvailableReplicas == state.Replicas {
			state.Phase = systemv1alpha1.Ready
		} else if state.AvailableReplicas > 0 {
			state.Phase = systemv1alpha1.Available
		} else if maxRetryNum != 0 && state.RetryNum > maxRetryNum {
			state.Phase = systemv1alpha1.OutOfMaxRetry
		} else if state.PendingDuration != nil && maxPendingDuration != 0 &&
			*state.PendingDuration > maxPendingDuration {
			state.Phase = systemv1alpha1.OutOfMaxPending
		} else if state.AvailableReplicas <= 0 {
			state.Phase = systemv1alpha1.NotReady
		}
	}
}

func (r *ResourceCostReconciler) initListeningServerStatus(status map[string]systemv1alpha1.ServerState, app *systemv1alpha1.Application) {
	serverState := systemv1alpha1.ServerState{Phase: systemv1alpha1.Paused}
	for serverName, servers := range app.Spec.Server {
		for _, server := range servers {
			//key为服务名-服务版本号
			statusKey := fmt.Sprintf("%s-%s", serverName, server.Version)
			status[statusKey] = serverState
		}
	}
}

func cleanPodHistory(status *systemv1alpha1.ResourceCostStatus) {
	for key, pods := range status.Pods {
		var beforePods []*systemv1alpha1.PodInfo
		for _, pod := range pods {
			if pod.StoppedTime != nil && pod.StoppedTime.Add(time.Hour*48).After(metav1.Now().Time) {
				beforePods = append(beforePods, pod)
			}
		}
		if len(beforePods) == 0 {
			delete(status.Pods, key)
		} else {
			status.Pods[key] = beforePods
		}
	}
}

func getPodContainerMainStatus(pod *corev1.Pod) (string, error) {
	totalContainers := len(pod.Spec.Containers)
	reason := string(pod.Status.Phase)
	if pod.Status.Reason != "" {
		reason = pod.Status.Reason
	}
	if pod.DeletionTimestamp != nil && reason == "Pending" {
		return "Terminated", nil
	}

	// If the Pod carries {type:PodScheduled, reason:WaitingForGates}, set reason to 'SchedulingGated'.
	for _, condition := range pod.Status.Conditions {
		if condition.Type == corev1.PodScheduled && condition.Reason == corev1.PodReasonSchedulingGated {
			reason = corev1.PodReasonSchedulingGated
		}
	}
	initContainers := make(map[string]*corev1.Container)

	for i := range pod.Spec.InitContainers {
		initContainers[pod.Spec.InitContainers[i].Name] = &pod.Spec.InitContainers[i]
		if isRestartableInitContainer(&pod.Spec.InitContainers[i]) {
			totalContainers++
		}
	}

	initReason, initializing := getInitContainerStatus(pod)
	if initializing {
		return initReason, nil
	}

	reason = getMainContainerStatus(pod, reason)

	if !initializing || isPodInitializedConditionTrue(&pod.Status) {
		reason = getMainContainerStatus(pod, reason)
	}

	reason = getDeletingPodReason(pod, reason)

	return reason, nil
}

func getDeletingPodReason(pod *corev1.Pod, reason string) string {
	if pod.DeletionTimestamp != nil && pod.Status.Reason == node.NodeUnreachablePodReason {
		reason = "Unknown"
	} else if pod.DeletionTimestamp != nil && reason == "Running" {
		reason = "Terminated"
	}
	return reason
}

func getInitContainerStatus(pod *corev1.Pod) (string, bool) {
	initContainers := make(map[string]*corev1.Container)
	for i := range pod.Spec.InitContainers {
		initContainers[pod.Spec.InitContainers[i].Name] = &pod.Spec.InitContainers[i]
	}

	for i := range pod.Status.InitContainerStatuses {
		container := pod.Status.InitContainerStatuses[i]
		switch {
		case container.State.Terminated != nil && container.State.Terminated.ExitCode == 0:
			continue
		case isRestartableInitContainer(initContainers[container.Name]) && container.Started != nil && *container.Started:
			continue
		case container.State.Terminated != nil:
			return "Init:Error", true
		case container.State.Waiting != nil && container.State.Waiting.Reason != "PodInitializing":
			return "Init:" + container.State.Waiting.Reason, true
		default:
			return "Init:PodInitializing", true
		}
	}
	return "", false
}

func getMainContainerStatus(pod *corev1.Pod, reason string) string {
	hasRunning := false
	for i := len(pod.Status.ContainerStatuses) - 1; i >= 0; i-- {
		container := pod.Status.ContainerStatuses[i]
		if container.State.Waiting != nil && container.State.Waiting.Reason != "" {
			reason = container.State.Waiting.Reason
		} else if container.State.Terminated != nil && container.State.Terminated.Reason != "" {
			reason = container.State.Terminated.Reason
		} else if container.State.Terminated != nil && container.State.Terminated.Reason == "" {
			reason = "Error"
		} else if container.Ready && container.State.Running != nil {
			hasRunning = true
		}
	}

	// Update pod status if there's at least one running container
	if reason == "Completed" && hasRunning {
		if hasPodReadyCondition(pod.Status.Conditions) {
			return "Running"
		}
		return "NotReady"
	}
	return reason
}

func isRestartableInitContainer(initContainer *corev1.Container) bool {
	if initContainer.RestartPolicy == nil {
		return false
	}
	return *initContainer.RestartPolicy == corev1.ContainerRestartPolicyAlways
}

func isPodInitializedConditionTrue(status *corev1.PodStatus) bool {
	for _, condition := range status.Conditions {
		if condition.Type != corev1.PodInitialized {
			continue
		}

		return condition.Status == corev1.ConditionTrue
	}
	return false
}

func hasPodReadyCondition(conditions []corev1.PodCondition) bool {
	for _, condition := range conditions {
		if condition.Type == corev1.PodReady && condition.Status == corev1.ConditionTrue {
			return true
		}
	}
	return false
}
