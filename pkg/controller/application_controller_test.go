package controller

import "time"

// import (
// 	"time"

// 	. "github.com/onsi/ginkgo/v2"
// 	. "github.com/onsi/gomega"
// )

const (
	timeout = time.Second * 10

	interval          = time.Second * 5
	appName           = "test-app"
	appNamespace      = "hero-user"
	testserver01      = "testserver01"
	appDeploymentName = "test-app-v1"
)

// var _ = Describe("ApplicationController", func() {

// 	var (
// 		//app    systemv1alpha1.Application
// 		ns          *corev1.Namespace
// 		deploy      *appv1.Deployment
// 		pod         *corev1.Pod
// 		initialized bool
// 	)
// 	BeforeEach(func() {
// 		// Add any setup steps that needs to be executed before each test
// 		app := constructionApp(appName)
// 		deploy = constructionDeployment(appDeploymentName, appName)
// 		pod = constructPod(appName, appDeploymentName)
// 		istioGateway := constructIstioGateway()
// 		//创建podgroup
// 		podGroup := &scheduling.PodGroup{
// 			ObjectMeta: metav1.ObjectMeta{
// 				Name:      appName,
// 				Namespace: appNamespace,
// 			},
// 			Spec: scheduling.PodGroupSpec{
// 				MinMember:     *app.Spec.Scheduler.MinAvailable,
// 				MinTaskMember: nil,
// 				Queue:         app.Spec.Scheduler.Queue,
// 			},
// 		}
// 		if !initialized {
// 			ns = &corev1.Namespace{
// 				ObjectMeta: metav1.ObjectMeta{
// 					Name: app.Namespace,
// 				},
// 			}
// 			// 检查命名空间是否已存在，并在需要时创建它
// 			Expect(k8sClient.Create(ctx, ns)).Should(Succeed())
// 			istions := &corev1.Namespace{
// 				ObjectMeta: metav1.ObjectMeta{
// 					Name: istio.GatewayNamespace,
// 				},
// 			}
// 			Expect(k8sClient.Create(ctx, istions)).Should(Succeed())

// 			//创建istio gateway
// 			Expect(k8sClient.Create(ctx, istioGateway)).Should(Succeed())
// 			Expect(k8sClient.Create(ctx, deploy)).Should(Succeed())
// 			Expect(k8sClient.Create(ctx, pod)).Should(Succeed())
// 			Expect(k8sClient.Create(ctx, podGroup)).Should(Succeed())
// 			Expect(k8sClient.Create(ctx, app)).Should(Succeed())

// 			initialized = true
// 		}
// 	})

// 	// Add Tests for OpenAPI validation (or additonal CRD features) specified in
// 	// your API definition.
// 	// Avoid adding tests for vanilla CRUD operations because they would
// 	// test Kubernetes API server, which isn't the goal here.
// 	Context("when app status is nil ", func() {
// 		It("app should be creating", func() {

// 			key := types.NamespacedName{
// 				Name:      "test-app",
// 				Namespace: "hero-user",
// 			}

// 			// Create
// 			By("Expecting submitted")
// 			Eventually(func() bool {
// 				createdSapp := &systemv1alpha1.Application{}
// 				k8sClient.Get(context.Background(), key, createdSapp)
// 				return createdSapp.Status.Phase == systemv1alpha1.Creating
// 			}, timeout, interval).Should(BeTrue())

// 		})
// 	})
// 	Context("when app status is creating ", func() {
// 		It("app should be running", func() {

// 			//err := k8sClient.Get(ctx, client.ObjectKeyFromObject(deploy), deploy)
// 			//Expect(err).Should(Succeed())
// 			//
// 			//deploy.Status = appv1.DeploymentStatus{
// 			//	AvailableReplicas: 1,
// 			//	Replicas:          1,
// 			//	ReadyReplicas:     1,
// 			//}
// 			//Expect(k8sClient.Status().Update(ctx, deploy)).Should(Succeed()) // Create
// 			key := types.NamespacedName{
// 				Name:      "test-app",
// 				Namespace: "hero-user",
// 			}
// 			createdSapp := &systemv1alpha1.Application{}
// 			k8sClient.Get(context.Background(), key, createdSapp)
// 			createdSapp.Status.AvailableServers = 1
// 			Expect(k8sClient.Status().Update(context.Background(), createdSapp)).Should(Succeed()) // Create

// 			By("Expecting running")
// 			Eventually(func() bool {
// 				k8sClient.Get(context.Background(), key, createdSapp)
// 				return createdSapp.Status.Phase == systemv1alpha1.Running
// 			}, timeout, interval).Should(BeTrue())
// 		})
// 	})

// 	Context("when app status is running ", func() {
// 		It("app should be listening", func() {

// 			key := types.NamespacedName{
// 				Name:      "test-app",
// 				Namespace: "hero-user",
// 			}

// 			createdSapp := &systemv1alpha1.Application{}
// 			k8sClient.Get(context.Background(), key, createdSapp)
// 			createdSapp.Status.ListeningServers = 1
// 			createdSapp.Status.AvailableServers = 0
// 			Expect(k8sClient.Status().Update(context.Background(), createdSapp)).Should(Succeed()) // Create
// 			By("Expecting listening")
// 			Eventually(func() bool {
// 				createdSapp := &systemv1alpha1.Application{}
// 				k8sClient.Get(context.Background(), key, createdSapp)
// 				return createdSapp.Status.Phase == systemv1alpha1.Listening
// 			}, timeout, interval).Should(BeTrue())
// 		})
// 	})

// 	Context("when app status is listening ", func() {
// 		It("app should be running", func() {

// 			key := types.NamespacedName{
// 				Name:      "test-app",
// 				Namespace: "hero-user",
// 			}
// 			createdSapp := &systemv1alpha1.Application{}
// 			err := k8sClient.Get(context.Background(), key, createdSapp)
// 			createdSapp.Status.AvailableServers = 1
// 			createdSapp.Status.ListeningServers = 0
// 			// 在更新前重新获取资源并确保没有版本冲突
// 			err = k8sClient.Status().Update(context.Background(), createdSapp)
// 			if err != nil && errors.IsConflict(err) {
// 				// 版本冲突时，重新获取资源并重试
// 				err = k8sClient.Get(context.Background(), key, createdSapp)
// 				Expect(err).ShouldNot(HaveOccurred())
// 				err = k8sClient.Status().Update(context.Background(), createdSapp)
// 			}
// 			Expect(err).ShouldNot(HaveOccurred())
// 			// 使用 Patch 操作更新状态，避免版本冲突
// 			By("Expecting runnning")
// 			Eventually(func() bool {
// 				createdSapp := &systemv1alpha1.Application{}
// 				k8sClient.Get(context.Background(), key, createdSapp)
// 				return createdSapp.Status.Phase == systemv1alpha1.Running
// 			}, time.Second*30, interval).Should(BeTrue())
// 		})
// 	})

// })

// func constructionApp(appName string) *systemv1alpha1.Application {
// 	return &systemv1alpha1.Application{
// 		TypeMeta: metav1.TypeMeta{
// 			APIVersion: "system.hero.ai/v1alpha1",
// 			Kind:       "Application",
// 		},
// 		ObjectMeta: metav1.ObjectMeta{
// 			Name:      appName,
// 			Namespace: appNamespace,
// 		},

// 		Spec: systemv1alpha1.ApplicationSpec{
// 			Version: "v1",
// 			ServiceMesh: &systemv1alpha1.ServiceMeshSpec{
// 				Backend:        "istio",
// 				IngressGateway: true,
// 				AutoMTLS:       true,
// 			},

// 			Gateway: &systemv1alpha1.GatewaySpec{
// 				Backend: "istio",
// 				Host:    appName,
// 			},

// 			Scheduler: systemv1alpha1.SchedulerSpec{
// 				SchedulerName: "volcano",
// 				MinAvailable:  ptr.To(int32(1)),
// 				MaxRetry:      ptr.To(int32(2)),
// 				Queue:         "default",
// 			},
// 			Server: map[string][]systemv1alpha1.ServerSpec{

// 				testserver01: []systemv1alpha1.ServerSpec{

// 					{
// 						ServiceMesh: &systemv1alpha1.ServiceMeshSpec{
// 							SubRoute: &systemv1alpha1.SubRoute{
// 								Weight:  nil,
// 								Match:   nil,
// 								Retries: nil,
// 								TrafficPolicy: &systemv1alpha1.TrafficPolicy{
// 									CircuitBreaker: &systemv1alpha1.CircuitBreaker{
// 										MaxConnections: 100,
// 									},
// 								},
// 							},
// 							Backend:        "istio",
// 							IngressGateway: true,
// 							AutoMTLS:       true,
// 						},
// 						Version: appName + "v1",
// 						Template: corev1.PodTemplateSpec{
// 							ObjectMeta: metav1.ObjectMeta{
// 								Labels: map[string]string{
// 									"server": appName,
// 									"app":    appName,
// 								},
// 							},
// 							Spec: corev1.PodSpec{
// 								Containers: []corev1.Container{
// 									{
// 										Name:            appName,
// 										Image:           "registry.cnbita.com:5000/gradio-ui/inferserver:v1",
// 										ImagePullPolicy: corev1.PullAlways,
// 										Ports: []corev1.ContainerPort{
// 											{
// 												Name:          "http1",
// 												ContainerPort: 5000,
// 											},
// 										},
// 										Env: []corev1.EnvVar{
// 											{
// 												Name:  "DEMO_GREETING",
// 												Value: "Hello from the environment",
// 											},
// 										},
// 										Resources: corev1.ResourceRequirements{
// 											Limits: corev1.ResourceList{
// 												corev1.ResourceCPU:    resource.MustParse("8"),
// 												corev1.ResourceMemory: resource.MustParse("16Gi"),
// 											},
// 											Requests: corev1.ResourceList{
// 												corev1.ResourceCPU:    resource.MustParse("1"),
// 												corev1.ResourceMemory: resource.MustParse("4Gi"),
// 											},
// 										},
// 										VolumeMounts: []corev1.VolumeMount{
// 											{
// 												Name:      "test",
// 												MountPath: "/data/b20240312182234882dppeky",
// 												SubPath:   "data/b20240312182234882dppeky",
// 											},
// 										},
// 									},
// 									{
// 										Name:            "gradio-container",
// 										Image:           "registry.cnbita.com:5000/gradio-ui/gradio-ui:v1",
// 										ImagePullPolicy: corev1.PullAlways,
// 										Ports: []corev1.ContainerPort{
// 											{
// 												ContainerPort: 7890,
// 											},
// 										},
// 									},
// 								},
// 								Volumes: []corev1.Volume{
// 									{
// 										Name: "test",
// 										VolumeSource: corev1.VolumeSource{
// 											PersistentVolumeClaim: &corev1.PersistentVolumeClaimVolumeSource{
// 												ClaimName: "b20240312182234882dppeky",
// 											},
// 										},
// 									},
// 								},
// 							},
// 						},
// 					},
// 				},
// 			},
// 		},
// 		Status: systemv1alpha1.ApplicationStatus{},
// 	}

// }

// func constructionDeployment(appDeployName, appName string) *v1.Deployment {

// 	return &v1.Deployment{
// 		TypeMeta: metav1.TypeMeta{
// 			APIVersion: "apps/v1",
// 			Kind:       "Deployment",
// 		},
// 		ObjectMeta: metav1.ObjectMeta{
// 			Name:      appDeployName,
// 			Namespace: appNamespace,
// 			Labels: map[string]string{
// 				"app": appName,
// 			},
// 		},

// 		Spec: appv1.DeploymentSpec{
// 			Selector: &metav1.LabelSelector{
// 				MatchLabels: map[string]string{
// 					"app": appName, // Make sure the selector matches the labels in the template
// 				},
// 			},
// 			Replicas: ptr.To(int32(1)),
// 			Template: corev1.PodTemplateSpec{
// 				ObjectMeta: metav1.ObjectMeta{
// 					Labels: map[string]string{
// 						"app": appName,
// 					},
// 				},

// 				Spec: corev1.PodSpec{
// 					Containers: []corev1.Container{
// 						{
// 							Name:            appName,
// 							Image:           "registry.cnbita.com:5000/gradio-ui/inferserver@sha256:fb711a8b2a50049cee4bc9c6d75522d1830c71e6c9af6719bc0db9c420385d19",
// 							ImagePullPolicy: corev1.PullAlways,
// 							Ports: []corev1.ContainerPort{
// 								{
// 									ContainerPort: 5000,
// 									Name:          "http1",
// 								},
// 							},
// 							Resources: corev1.ResourceRequirements{
// 								Limits: corev1.ResourceList{
// 									corev1.ResourceCPU:    resource.MustParse("1"),
// 									corev1.ResourceMemory: resource.MustParse("4Gi"),
// 								},
// 								Requests: corev1.ResourceList{
// 									corev1.ResourceCPU:    resource.MustParse("1"),
// 									corev1.ResourceMemory: resource.MustParse("4Gi"),
// 								},
// 							},
// 						},
// 					},
// 				},
// 			},
// 		},
// 	}
// }

// func constructPod(sappName, sappDeployName string) *corev1.Pod {
// 	return &corev1.Pod{
// 		TypeMeta: metav1.TypeMeta{
// 			APIVersion: "v1",
// 			Kind:       "Pod",
// 		},
// 		ObjectMeta: metav1.ObjectMeta{
// 			Name:      sappDeployName + "-pod",
// 			Namespace: appNamespace,
// 			Labels: map[string]string{
// 				"app": sappName,
// 			},
// 			Annotations: map[string]string{
// 				"deployment.kubernetes.io/revision": "1",
// 				"serving.knative.dev/creator":       "kubernetes-admin",
// 			},
// 		},
// 		Spec: corev1.PodSpec{
// 			Containers: []corev1.Container{
// 				{
// 					Name:            sappName,
// 					Image:           "registry.cnbita.com:5000/gradio-ui/inferserver@sha256:fb711a8b2a50049cee4bc9c6d75522d1830c71e6c9af6719bc0db9c420385d19",
// 					ImagePullPolicy: corev1.PullAlways,
// 					Ports: []corev1.ContainerPort{
// 						{
// 							ContainerPort: 5000,
// 							Name:          "http1",
// 						},
// 					},
// 					Env: []corev1.EnvVar{
// 						{
// 							Name:  "DEMO_GREETING",
// 							Value: "Hello from the environment",
// 						},
// 						{
// 							Name:  "K_REVISION",
// 							Value: "sapp-test-v1",
// 						},
// 						{
// 							Name:  "K_CONFIGURATION",
// 							Value: "sapp-test-v1",
// 						},
// 						{
// 							Name:  "K_SERVICE",
// 							Value: "sapp-test",
// 						},
// 					},
// 					Resources: corev1.ResourceRequirements{
// 						Limits: corev1.ResourceList{
// 							corev1.ResourceCPU:    resource.MustParse("1"),
// 							corev1.ResourceMemory: resource.MustParse("4Gi"),
// 						},
// 						Requests: corev1.ResourceList{
// 							corev1.ResourceCPU:    resource.MustParse("1"),
// 							corev1.ResourceMemory: resource.MustParse("4Gi"),
// 						},
// 					},
// 				},
// 			},
// 		},
// 	}
// }

// func constructIstioGateway() *clientnetworking.Gateway {

// 	gateway := &clientnetworking.Gateway{
// 		ObjectMeta: metav1.ObjectMeta{
// 			Name:      istio.GatewayName,
// 			Namespace: istio.GatewayNamespace,
// 		},
// 		Spec: networkingv1alpha3.Gateway{
// 			Selector: map[string]string{istio.SelectorKey: istio.SelectorValue},
// 			Servers: []*networkingv1alpha3.Server{
// 				{
// 					Port: &networkingv1alpha3.Port{
// 						Number:   80,
// 						Name:     "http",
// 						Protocol: "http",
// 					},
// 					Hosts: []string{"*"},
// 				},
// 			},
// 		},
// 	}
// 	return gateway
// }
