package controller

//import (

//)
//
//// Define utility constants for object names and testing timeouts/durations and intervals.
//const (
//	timeout  = time.Second * 10
//	duration = time.Second * 10
//	interval = time.Millisecond * 250
//)
//
//var _ = Describe("Deployment Create", func() {
//
//	var app *appv1alpha1.Application
//	var ns *corev1.Namespace
//
//	BeforeEach(func() {
//		app = CreateApplication()
//		ns = &corev1.Namespace{
//			ObjectMeta: metav1.ObjectMeta{
//				Name: app.Namespace,
//			},
//		}
//		Expect(k8sClient.Create(context.TODO(), ns)).Should(Succeed())
//		Expect(k8sClient.Create(context.TODO(), app)).Should(Succeed())
//	})
//
//	AfterEach(func() {
//		Expect(k8sClient.Delete(context.TODO(), app)).Should(Succeed())
//		Expect(k8sClient.Delete(context.TODO(), ns)).Should(Succeed())
//	})
//
//	It("should create a deployment for each server", func() {
//		// 验证 Deployment 创建
//		for serverName, servers := range app.Spec.Server {
//			for _, serverSpec := range servers {
//				deploymentName := fmt.Sprintf("%s-%s", serverName, serverSpec.Version)
//				deployment := &v1.Deployment{}
//				Eventually(func() error {
//					return k8sClient.Get(context.TODO(), client.ObjectKey{
//						Namespace: app.Namespace,
//						Name:      deploymentName,
//					}, deployment)
//				}, timeout, interval).Should(Succeed())
//				By(fmt.Sprintf("Checking configuration of deployment %s", deploymentName))
//				Expect(deployment.Spec.Replicas).To(Equal(serverSpec.Replicas), "Replica count should match for "+deploymentName)
//				// check  deploymnet  podtemplate label
//				podTemplateLabel := deployment.Spec.Template.Labels
//
//				Expect(podTemplateLabel[appv1alpha1.LabelApplicationName]).To(Equal(app.Name))
//
//				Expect(podTemplateLabel[appv1alpha1.LabelAppName]).To(Equal(serverName))
//
//				Expect(podTemplateLabel[appv1alpha1.LabelAppversion]).To(Equal(serverSpec.Version))
//
//				Expect(deployment.Name).To(Equal(fmt.Sprintf("%s-%s", serverName, serverSpec.Version)), "deployment Name  should match for  server name with - server version")
//				Expect(deployment.Spec.Template.Spec.Containers[0].Image).To(Equal(serverSpec.Template.Spec.Containers[0].Image), "Container image should match for "+deploymentName)
//
//			}
//		}
//	})
//})
//
//func CreateApplication() *appv1alpha1.Application {
//	var wg int32 = 100
//	var wg1 int32 = 80
//	var wg2 int32 = 20
//	var num int32 = 1
//	app := &appv1alpha1.Application{
//		TypeMeta: metav1.TypeMeta{
//			APIVersion: "system.hero.ai/v1alpha1",
//			Kind:       "Application",
//		},
//		ObjectMeta: metav1.ObjectMeta{
//			Name:      "llm-predict",
//			Namespace: "llm-example",
//		},
//
//		Spec: appv1alpha1.ApplicationSpec{
//			Version: "v1",
//			ServiceMesh: &appv1alpha1.ServiceMeshSpec{
//				Backend: appv1alpha1.Istio,
//				SubRoute: &appv1alpha1.SubRoute{
//					Weight: &wg,
//					Retries: &appv1alpha1.HTTPRetry{
//						AutoRetire: true,
//						Attempts:   3,
//					},
//					TrafficPolicy: &appv1alpha1.TrafficPolicy{
//						LoadBalancer: 4,
//						CircuitBreaker: &appv1alpha1.CircuitBreaker{
//							MaxConnections:          100,
//							HTTP1MaxPendingRequests: 100,
//							ConsecutiveErrors:       5,
//							Interval:                &metav1.Duration{Duration: time.Second * 5},
//							BaseEjectionTime:        &metav1.Duration{Duration: time.Minute * 5},
//							MaxEjectionPercent:      100,
//						},
//					},
//				},
//			},
//			AutoScaling: &appv1alpha1.AutoScalingSpec{
//				Backend:        appv1alpha1.Keda,
//				MinReplicas:    &num,
//				MaxReplicas:    &num,
//				CooldownPeriod: &wg1,
//				Triggers: []appv1alpha1.ScaleTriggers{
//					{
//						Type:     "cpu",
//						Metadata: map[string]string{"Utilization": "50"},
//					},
//				},
//				Serverless: []appv1alpha1.ScaleTriggers{
//					{
//						Type:     "prometheus",
//						Metadata: map[string]string{"http_requests_total": "20"},
//					},
//				},
//			},
//			Scheduler: appv1alpha1.SchedulerSpec{
//				SchedulerName: appv1alpha1.KubeScheduler,
//			},
//			Volumes: []appv1alpha1.VolumeSpec{
//				{
//					MountPath:       "/data",
//					VolumeClaimName: "test-data",
//					VolumeClaim: &corev1.PersistentVolumeClaimSpec{
//						AccessModes: []corev1.PersistentVolumeAccessMode{corev1.ReadWriteOnce},
//						Resources: corev1.ResourceRequirements{
//							Requests: corev1.ResourceList{},
//						},
//					},
//				},
//			},
//			Gateway: &appv1alpha1.GatewaySpec{
//				Backend: appv1alpha1.IstioGateway,
//				Host:    "llm-predict",
//			},
//			Server: map[string][]appv1alpha1.ServerSpec{
//				"image-describe": {
//					{
//						Version:  "v1",
//						Replicas: pointer.Int32Ptr(1),
//						Template: corev1.PodTemplateSpec{
//							ObjectMeta: metav1.ObjectMeta{
//								Labels: map[string]string{"app": "image-describe"},
//							},
//							Spec: corev1.PodSpec{
//								Containers: []corev1.Container{
//									{
//										Name:            "image-describe",
//										Image:           "registry.cn-hangzhou.aliyuncs.com/ruanxingbaozi/pytorch:image_recognition_demo",
//										ImagePullPolicy: corev1.PullAlways,
//										Args:            []string{"bash", "-c", "python image_recognition_server.py"},
//										Ports: []corev1.ContainerPort{
//											{
//												Name:          "http",
//												ContainerPort: 5000,
//											},
//										},
//									},
//								},
//							},
//						},
//						ServiceMesh: &appv1alpha1.ServiceMeshSpec{
//							Backend: appv1alpha1.Istio,
//							SubRoute: &appv1alpha1.SubRoute{
//								Weight: &wg,
//								Retries: &appv1alpha1.HTTPRetry{
//									AutoRetire: true,
//									Attempts:   3,
//								},
//							},
//						},
//					},
//				},
//				"image-predict": {
//					{
//						Version:  "v1",
//						Replicas: pointer.Int32Ptr(1),
//						ServiceMesh: &appv1alpha1.ServiceMeshSpec{
//							SubRoute: &appv1alpha1.SubRoute{
//								Weight: &wg1,
//							},
//						},
//						Template: corev1.PodTemplateSpec{
//							ObjectMeta: metav1.ObjectMeta{
//								Labels: map[string]string{"app": "image-predict"},
//							},
//							Spec: corev1.PodSpec{
//								Containers: []corev1.Container{
//									{
//										Name:            "image-predict",
//										Image:           "registry.cn-hangzhou.aliyuncs.com/ruanxingbaozi/pytorch:image_recognition_demo",
//										ImagePullPolicy: corev1.PullAlways,
//										Args:            []string{"bash", "-c", "python image_classification_server.py"},
//										Ports: []corev1.ContainerPort{
//											{
//												Name:          "http",
//												ContainerPort: 5000,
//											},
//										},
//									},
//								},
//							},
//						},
//					},
//					{
//						Version:  "v2",
//						Replicas: pointer.Int32Ptr(1),
//						ServiceMesh: &appv1alpha1.ServiceMeshSpec{
//							SubRoute: &appv1alpha1.SubRoute{
//								Weight: &wg2,
//							},
//						},
//						Template: corev1.PodTemplateSpec{
//							ObjectMeta: metav1.ObjectMeta{
//								Labels: map[string]string{"app": "image-predict"},
//							},
//							Spec: corev1.PodSpec{
//								Containers: []corev1.Container{
//									{
//										Name:            "image-predict",
//										Image:           "registry.cn-hangzhou.aliyuncs.com/ruanxingbaozi/pytorch:image_recognition_demo",
//										ImagePullPolicy: corev1.PullAlways,
//										Args:            []string{"bash", "-c", "python image_classification_server.py"},
//										Ports: []corev1.ContainerPort{
//											{
//												Name:          "http",
//												ContainerPort: 5000,
//											},
//										},
//									},
//								},
//							},
//						},
//					},
//				},
//				"llm-predict": {
//					{
//						Version:  "v1",
//						Replicas: pointer.Int32Ptr(1),
//						ServiceMesh: &appv1alpha1.ServiceMeshSpec{
//							SubRoute: &appv1alpha1.SubRoute{
//								Weight: &wg2,
//							},
//						},
//						Template: corev1.PodTemplateSpec{
//							ObjectMeta: metav1.ObjectMeta{
//								Labels: map[string]string{"app": "llm-predict"},
//							},
//							Spec: corev1.PodSpec{
//								Containers: []corev1.Container{
//									{
//										Name:            "llm-predict",
//										Image:           "registry.cn-hangzhou.aliyuncs.com/ruanxingbaozi/pytorch:image_recognition_demo",
//										ImagePullPolicy: corev1.PullAlways,
//										Args:            []string{"bash", "-c", "python llm_server.py"},
//										Ports: []corev1.ContainerPort{
//											{
//												Name:          "http",
//												ContainerPort: 5000,
//											},
//										},
//									},
//								},
//							},
//						},
//					},
//				},
//				"llm-inference-app": {
//					{
//						Version:  "v1",
//						Replicas: pointer.Int32Ptr(1),
//						Template: corev1.PodTemplateSpec{
//							ObjectMeta: metav1.ObjectMeta{
//								Labels: map[string]string{"app": "llm-inference-app"},
//							},
//							Spec: corev1.PodSpec{
//								Containers: []corev1.Container{
//									{
//										Name:            "llm-inference-app",
//										Image:           "registry.cn-hangzhou.aliyuncs.com/ruanxingbaozi/pytorch:image_recognition_demo",
//										ImagePullPolicy: corev1.PullAlways,
//										Args:            []string{"bash", "-c", "python app.py"},
//										Ports: []corev1.ContainerPort{
//											{
//												Name:          "http",
//												ContainerPort: 7860,
//											},
//										},
//									},
//								},
//							},
//						},
//					},
//				},
//			},
//		},
//	}
//	return app
//}
