package controller

import (
	"context"
	"fmt"
	"sync"
	"time"

	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	ref "k8s.io/client-go/tools/reference"
	"k8s.io/klog"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

var (
	rd          *removeDup
	idleTimeout = 300 * time.Second
	timeCheck   = 60 * time.Second
	prefix      = "job-"
)

type removeDup struct {
	data       map[string]time.Time
	lastEvents map[string]string
	timeout    time.Duration
	mutex      sync.Mutex
	timer      *time.Timer
}

func newRemoveDup(timeout time.Duration) *removeDup {
	rp := &removeDup{
		data:       make(map[string]time.Time),
		lastEvents: make(map[string]string),
		timeout:    timeout,
	}

	rp.startClean()
	return rp
}

func (rd *removeDup) Add(key string) {
	rd.mutex.Lock()
	defer rd.mutex.Unlock()
	rd.data[key] = time.Now().Add(rd.timeout)
}

func (rd *removeDup) Exists(key string) bool {
	rd.mutex.Lock()
	defer rd.mutex.Unlock()
	if _, ok := rd.data[key]; !ok {
		return false
	}

	return true
}

func (rd *removeDup) startClean() {
	rd.timer = time.NewTimer(timeCheck)
	go func() {
		for {
			<-rd.timer.C
			rd.mutex.Lock()
			expiredKeys := make([]string, 0)
			now := time.Now()
			for key, expiration := range rd.data {
				if now.After(expiration) {
					expiredKeys = append(expiredKeys, key)
				}
			}
			for _, key := range expiredKeys {
				delete(rd.data, key)
			}
			rd.mutex.Unlock()
			rd.timer.Reset(timeCheck)
		}
	}()
}

func init() {
	rd = newRemoveDup(idleTimeout)
}

func (rd *removeDup) GetLastEvent(objKey string) (string, bool) {
	rd.mutex.Lock()
	defer rd.mutex.Unlock()
	lastEvent, found := rd.lastEvents[objKey]
	return lastEvent, found
}

func (rd *removeDup) SetLastEvent(objKey, eventKey string) {
	rd.mutex.Lock()
	defer rd.mutex.Unlock()
	rd.lastEvents[objKey] = eventKey
}
func NewEventRecord(cli client.Client, scheme *runtime.Scheme) *EventRecord {
	return &EventRecord{
		client: cli,
		scheme: scheme,
	}
}

type EventRecord struct {
	client client.Client
	scheme *runtime.Scheme
	reason string
}

func (e *EventRecord) eventRecord(ctx context.Context, obj client.Object, eventtype, reason, message string) error {
	if obj == nil {
		return fmt.Errorf("object is nil")
	}

	if e == nil {
		return fmt.Errorf("eventRecord is nil")
	}

	if rd == nil {
		return fmt.Errorf("rd is not initialized")
	}

	// 使用对象名称、事件类型、原因和当前状态作为键
	key := prefix + obj.GetName() + eventtype + reason + message + e.reason

	// 获取对象的上一次事件状态
	lastEventKey, found := rd.GetLastEvent(prefix + obj.GetName())
	if !found {
		lastEventKey = ""
	}

	// 检查当前事件是否与上一次事件相同
	if key != lastEventKey {
		// 如果当前事件与上一次事件不同，记录该事件
		rd.SetLastEvent(prefix+obj.GetName(), key)

		ref, err := ref.GetReference(e.scheme, obj)
		if err != nil {
			klog.Errorf("%s getReference failed: %s", obj.GetObjectKind().GroupVersionKind().Kind, err.Error())
			return err
		}
		evt := e.makeEvent(ref, nil, eventtype, reason, message, obj.GetObjectKind().GroupVersionKind().Kind)
		return e.client.Create(ctx, evt)
	}

	klog.Infof("event for job %s already exists", obj.GetName())
	return nil
}

//func (e *EventRecord) eventRecord(ctx context.Context, obj client.Object, eventtype, reason, message string) error {
//	if obj == nil {
//		return fmt.Errorf("object is nil")
//	}
//
//	if e == nil {
//		return fmt.Errorf("eventRecord is nil")
//	}
//
//	if rd == nil {
//		return fmt.Errorf("rd is not initialized")
//	}
//
//	// 使用对象名称、事件类型、原因和当前状态作为键
//	key := prefix + obj.GetName() + eventtype + reason + message + e.reason
//
//	if !rd.Exists(key) {
//		rd.Add(key)
//
//		ref, err := ref.GetReference(e.scheme, obj)
//		if err != nil {
//			klog.Errorf("%s getReference failed: %s", obj.GetObjectKind().GroupVersionKind().Kind, err.Error())
//			return err
//		}
//		evt := e.makeEvent(ref, nil, eventtype, reason, message, obj.GetObjectKind().GroupVersionKind().Kind)
//		return e.client.Create(ctx, evt)
//	}
//	//解决事件重启后无法再触及重启后事件重复推送的问题
//	if v, found := obj.GetAnnotations()[AnnotationActionKey]; found && v == ActionStartValue {
//		// 使用不同的键来记录启动事件，确保状态变化后的启动事件被记录
//		startKey := key + ActionStartValue
//		if !rd.Exists(startKey) {
//			rd.Add(startKey)
//			ref, err := ref.GetReference(e.scheme, obj)
//			if err != nil {
//				klog.Errorf("%s getReference failed: %s", obj.GetObjectKind().GroupVersionKind().Kind, err.Error())
//				return err
//			}
//			// 创建事件
//			evt := e.makeEvent(ref, nil, eventtype, reason, message, obj.GetObjectKind().GroupVersionKind().Kind)
//
//			// 检查并处理注解
//			if annotations := obj.GetAnnotations(); annotations != nil {
//				evt.Annotations = annotations
//			}
//			return e.client.Create(ctx, evt)
//		}
//	}
//
//	// 如果事件已经存在，检查是否有启动注解
//	if v, found := obj.GetAnnotations()[AnnotationActionKey]; found && v == ActionStopValue {
//		// 使用不同的键来记录启动事件，确保状态变化后的启动事件被记录
//		stopKey := key + ActionStopValue
//		if !rd.Exists(stopKey) {
//			rd.Add(stopKey)
//
//			ref, err := ref.GetReference(e.scheme, obj)
//			if err != nil {
//				klog.Errorf("%s getReference failed: %s", obj.GetObjectKind().GroupVersionKind().Kind, err.Error())
//				return err
//			}
//
//			// 创建事件
//			evt := e.makeEvent(ref, nil, eventtype, reason, message, obj.GetObjectKind().GroupVersionKind().Kind)
//
//			// 检查并处理注解
//			if annotations := obj.GetAnnotations(); annotations != nil {
//				evt.Annotations = annotations
//			}
//
//			return e.client.Create(ctx, evt)
//		}
//	}
//
//	klog.Infof("event for job %s already exists", obj.GetName())
//	return nil
//}

func (e *EventRecord) makeEvent(ref *v1.ObjectReference, annotations map[string]string, eventtype, reason, message, reskind string) *v1.Event {
	t := metav1.Time{Time: time.Now()}
	namespace := ref.Namespace
	if namespace == "" {
		namespace = metav1.NamespaceDefault
	}
	return &v1.Event{
		ObjectMeta: metav1.ObjectMeta{
			Name:        fmt.Sprintf("%v.%x", ref.Name, t.UnixNano()),
			Namespace:   namespace,
			Annotations: annotations,
			Labels:      map[string]string{"system.hero.ai": "event"},
		},
		InvolvedObject: *ref,
		Reason:         reason,
		Message:        message,
		FirstTimestamp: t,
		LastTimestamp:  t,
		Type:           eventtype,
		Source: v1.EventSource{
			Component: reskind,
		},
	}
}
