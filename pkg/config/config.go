package config

import (
	"context"
	"encoding/base64"
	"fmt"
	"os"

	jobconfig "gitlab.bitahub.com/hero-os/hero-os-util/config"
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

var (
	svcDataID  = "application-controller-config.yaml"
	secretName = "image-secret"
)

//本地调试设置的配置文件路径
// var (
// 	cfgFile1 string = "/job-controllers/configs/job-controller-config.yaml"
// )

var (
	cfg *jobconfig.Config
	SC  = &ServerConfig{}
	EC  = &EnvConfig{}
)

type ServerConfig struct {
	Namespace         string `yaml:"Namespace"`
	HostSuffix        string `yaml:"HostSuffix"`
	EnableWebhooks    string `yaml:"EnableWebhooks"`
	LicenseServiceURL string `yaml:"LicenseServiceURL"`
	Prometheus        string `yaml:"Prometheus"`
}

type EnvConfig struct {
	Image   ImageConfig `yaml:"Image"`
	Cluster ClusterInfo `yaml:"CLUSTER"`
}

type ClusterInfo struct {
	IP string `yaml:"IP"`
}

type ImageConfig struct {
	ImageRepo string `yaml:"Domain"`
	UserName  string `yaml:"UserName"`
	Password  string `yaml:"Password"`
}

func CreateOrUpdateResource(apiReader client.Reader, client client.Client) error {
	return EC.createOrUpdateResource(apiReader, client)
}

//func (ecf *EnvConfig) createOrUpdateResource(client client.Client) error {
//	var secret v1.Secret
//	err := client.Get(context.TODO(), types.NamespacedName{
//		Name:      secretName,
//		Namespace: SC.Namespace,
//	}, &secret)
//	auth := base64Encode(fmt.Sprintf("%s:%s", EC.Image.UserName, EC.Image.Password))
//	value := fmt.Sprintf(`{"auths":{"%s":{"username":"%s","password":"%s","auth":"%s"}}}`, EC.Image.ImageRepo, EC.Image.UserName, EC.Image.Password, string(auth))
//	//value := fmt.Sprintf(`{"auths":{"%s":{"auth":"%s"}}}`, EC.Image.ImageRepo, string(auth))
//	if err != nil {
//		secret = v1.Secret{
//			ObjectMeta: metav1.ObjectMeta{
//				Name:      secretName,
//				Namespace: SC.getNamespace(),
//			},
//			Data: map[string][]byte{
//				v1.DockerConfigJsonKey: []byte(value),
//			},
//			Type: v1.SecretTypeDockerConfigJson,
//		}
//		client.Delete(context.TODO(), &secret)
//		return client.Create(context.TODO(), &secret)
//	}
//
//	secretPara := &v1.Secret{
//		ObjectMeta: metav1.ObjectMeta{
//			Name:      secretName,
//			Namespace: SC.Namespace,
//		},
//		Data: map[string][]byte{
//			v1.DockerConfigJsonKey: []byte(value),
//		},
//		Type: v1.SecretTypeDockerConfigJson,
//	}
//
//	return client.Update(context.TODO(), secretPara)
//}

func (ecf *EnvConfig) createOrUpdateResource(apiReader client.Reader, client client.Client) error {
	var secret v1.Secret
	// 使用非缓存客户端进行查询
	//修复the cache is not started, can not read objects
	err := apiReader.Get(context.TODO(), types.NamespacedName{
		Name:      secretName,
		Namespace: SC.Namespace,
	}, &secret)

	auth := base64Encode(fmt.Sprintf("%s:%s", EC.Image.UserName, EC.Image.Password))
	value := fmt.Sprintf(`{"auths":{"%s":{"username":"%s","password":"%s","auth":"%s"}}}`, EC.Image.ImageRepo, EC.Image.UserName, EC.Image.Password, auth)

	if err != nil {
		if errors.IsNotFound(err) {
			// Secret 不存在，使用常规客户端创建它
			secret = v1.Secret{
				ObjectMeta: metav1.ObjectMeta{
					Name:      secretName,
					Namespace: SC.Namespace,
				},
				Data: map[string][]byte{
					v1.DockerConfigJsonKey: []byte(value),
				},
				Type: v1.SecretTypeDockerConfigJson,
			}
			return client.Create(context.TODO(), &secret)
		}
		// 其他错误
		return err
	}

	// Secret 已存在，使用常规客户端更新它
	secret.Data[v1.DockerConfigJsonKey] = []byte(value)
	return client.Update(context.TODO(), &secret)
}

func base64Encode(data string) []byte {
	n := base64.StdEncoding.EncodedLen(len(data))
	dst := make([]byte, n)
	base64.StdEncoding.Encode(dst, []byte(data))
	return dst
}

func init() {
	cfg = jobconfig.NewConfig(jobconfig.SetConfig(
		jobconfig.SetNacosConfig(os.Getenv("NACOS_SERVER_URL"), os.Getenv("JASYPT_ENCRYPTOR_PASSWORD"), os.Getenv("INIT_MODE")),
		jobconfig.SetConfigSource(svcDataID, SC),
		jobconfig.SetCommonConfig(EC),
	))

	if err := cfg.InitConfig(); err != nil {
		panic(err)
	}
}
