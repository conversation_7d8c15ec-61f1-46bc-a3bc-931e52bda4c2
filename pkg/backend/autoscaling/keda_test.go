package autoscaling

import (
	appv1alpha1 "gitlab.leinao.ai/application-controller/apis/application/v1alpha1"
)

var kedaAdapter *KedaAutoScaling
var app *appv1alpha1.Application

// func init() {
// 	var scheme = runtime.NewScheme()
// 	utilruntime.Must(clientgoscheme.AddToScheme(scheme))
// 	utilruntime.Must(keda.AddToScheme(scheme))
// 	utilruntime.Must(appv1alpha1.AddToScheme(scheme))
// 	var wg1 int32 = 80
// 	var num int32 = 1
// 	app = &appv1alpha1.Application{
// 		TypeMeta: metav1.TypeMeta{
// 			APIVersion: "system.hero.ai/v1alpha1",
// 			Kind:       "Application",
// 		},
// 		ObjectMeta: metav1.ObjectMeta{
// 			Name:      "llm-predict",
// 			Namespace: "llm-example",
// 		},

// 		Spec: appv1alpha1.ApplicationSpec{
// 			Server: map[string][]appv1alpha1.ServerSpec{
// 				"image-describe": {
// 					{
// 						Version:  "v1",
// 						Replicas: pointer.Int32Ptr(1),
// 						Template: corev1.PodTemplateSpec{
// 							ObjectMeta: metav1.ObjectMeta{
// 								Labels: map[string]string{"app": "image-describe"},
// 							},
// 							Spec: corev1.PodSpec{
// 								Containers: []corev1.Container{
// 									{
// 										Name:            "image-describe",
// 										Image:           "registry.cn-hangzhou.aliyuncs.com/ruanxingbaozi/pytorch:image_recognition_demo",
// 										ImagePullPolicy: corev1.PullAlways,
// 										Args:            []string{"bash", "-c", "python image_recognition_server.py"},
// 										Ports: []corev1.ContainerPort{
// 											{
// 												Name:          "http",
// 												ContainerPort: 5000,
// 											},
// 										},
// 									},
// 								},
// 							},
// 						},
// 						AutoScaling: &appv1alpha1.AutoScalingSpec{
// 							Backend:        appv1alpha1.Keda,
// 							MinReplicas:    &num,
// 							MaxReplicas:    &num,
// 							CooldownPeriod: &wg1,
// 							Triggers: []appv1alpha1.ScaleTriggers{
// 								{
// 									Type:     "cpu",
// 									Metadata: map[string]string{"Utilization": "50"},
// 								},
// 							},
// 							Serverless: []appv1alpha1.ScaleTriggers{
// 								{
// 									Type:     "prometheus",
// 									Metadata: map[string]string{"http_requests_total": "20"},
// 								},
// 							},
// 						},
// 					},
// 				},
// 			},
// 		},
// 	}
// 	kedaScaleObject := &keda.ScaledObject{
// 		ObjectMeta: metav1.ObjectMeta{
// 			Name:      fmt.Sprintf("%s-%s", "image-describe", "v1"),
// 			Namespace: app.Namespace,
// 		},
// 	}

// 	objs := []client.Object{
// 		app, kedaScaleObject,
// 	}
// 	kedaAdapter = &KedaAutoScaling{
// 		Builder: autoscaling.NewAutoscalingBuilder(),
// 	}
// 	kedaAdapter.Setup(
// 		fake.NewClientBuilder().WithScheme(scheme).WithObjects(objs...).Build(),
// 		scheme, "127.0.0.1",
// 	)
// }

// func Test_Configure(t *testing.T) {
// 	err := kedaAdapter.Configure(app)
// 	if err != nil {
// 		t.Errorf("Unexpected error: %v", err)
// 	}
// }

// func Test_Name(t *testing.T) {
// 	name := kedaAdapter.Name()
// 	if name != "keda-2.11" {
// 		t.Errorf("Unexpected result: %v", name)
// 	}
// }

// func Test_Delete(t *testing.T) {
// 	err := kedaAdapter.Clean(app)
// 	if err != nil {
// 		t.Errorf("Unexpected error: %v", err)
// 	}
// }
