package autoscaling

import (
	"context"
	"fmt"

	kedav1alpha1 "github.com/kedacore/keda/v2/apis/keda/v1alpha1"
	"gitlab.leinao.ai/application-controller/apis/application/v1alpha1"
	"gitlab.leinao.ai/application-controller/pkg/backend/assembly/autoscaling"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/klog/v2"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"
)

type KedaAutoScaling struct {
	kedaClient client.Client
	*autoscaling.Builder
	scheme            *runtime.Scheme
	prometheusAddress string
}

func (keda *KedaAutoScaling) Setup(client client.Client, scheme *runtime.Scheme, prometheusAddress string) {
	keda.kedaClient = client
	keda.scheme = scheme
	keda.prometheusAddress = prometheusAddress
}

func (keda *KedaAutoScaling) Configure(app *v1alpha1.Application) error {
	// 为每个服务器配置 ScaledObject
	for serverName, servers := range app.Spec.Server {
		for _, server := range servers {
			if server.AutoScaling.Backend == v1alpha1.Keda && (server.AutoScaling.Triggers != nil || server.AutoScaling.Serverless != nil) {
				kedaScaleObject := &kedav1alpha1.ScaledObject{
					ObjectMeta: metav1.ObjectMeta{
						Name:      fmt.Sprintf("%s-%s", serverName, server.Version),
						Namespace: app.Namespace,
					},
				}
				//1 如果只开启了弹性伸缩hpa，则只创建scaleObject  // 创建 Keda 的 ScaledObjectSpec
				if _, err := controllerutil.CreateOrUpdate(context.Background(), keda.kedaClient, kedaScaleObject, keda.MutateScaledObjectFn(kedaScaleObject, app, serverName, server)); err != nil {
					klog.Errorf("Error creating: %v", err)
					return err
				}
			}

			//如果开了 serverless 则创建 HTTPScaledObject 的 CR
			//if server.AutoScaling.Serverless != nil {
			//	httpsScaleObject := &httpv1alpha1.HTTPScaledObject{
			//		ObjectMeta: metav1.ObjectMeta{
			//			Name:      fmt.Sprintf("%s-%s", serverName, server.Version),
			//			Namespace: app.Namespace,
			//		},
			//	}
			//	if _, err := controllerutil.CreateOrUpdate(context.Background(), keda.kedaClient, httpsScaleObject, keda.MutateHTTPScaledObjectFn(httpsScaleObject, app, serverName, server)); err != nil {
			//		return err
			//	}
			//}
		}
	}

	return nil
}

func (keda *KedaAutoScaling) MutateScaledObjectFn(scaledObj *kedav1alpha1.ScaledObject, app *v1alpha1.Application, serverName string, server v1alpha1.ServerSpec) controllerutil.MutateFn {
	return func() error {
		keda.BuildScaledObject(scaledObj, app, serverName, server, keda.prometheusAddress)
		scaledObj.SetOwnerReferences(nil)
		return controllerutil.SetControllerReference(app, scaledObj, keda.scheme)
	}
}

//func (keda *KedaAutoScaling) MutateHTTPScaledObjectFn(httpScaledObj *httpv1alpha1.HTTPScaledObject, app *v1alpha1.Application, serverName string, server v1alpha1.ServerSpec) controllerutil.MutateFn {
//	return func() error {
//		keda.BuildHTTPScaledObject(httpScaledObj, app, serverName, server)
//		httpScaledObj.SetOwnerReferences(nil)
//		return controllerutil.SetControllerReference(app, httpScaledObj, keda.scheme)
//	}
//}

func (keda *KedaAutoScaling) Name() string {
	return "keda-2.11"
}

func (keda *KedaAutoScaling) Inject(app *v1alpha1.Application) error {
	for _, servers := range app.Spec.Server {
		for i := range servers {
			// 直接使用指针修改  //如果server里配置了，就直接使用server的配置，没配置就把application里的赋值过来

			//每个子服务的autoscaling为nil,因为二者必须配置了一个才能走进来，所以只需要考虑单个等于nil的情况
			//需要考虑空指针问题
			//1.每个子服务不为nil,但是全局的app.Spec.AutoScaling为nil，这种情况直接return
			if app.Spec.AutoScaling == nil {
				return nil
			}
			servers[i].AutoScaling = merge(servers[i].AutoScaling, app.Spec.AutoScaling)
		}
	}
	return nil
}

func (keda *KedaAutoScaling) Clean(app *v1alpha1.Application) error {

	for serverName, servers := range app.Spec.Server {
		for _, server := range servers {
			handleError := func(err error, objType, name string) bool {
				if client.IgnoreNotFound(err) == nil {
					return false // 如果是 NotFound 错误，忽略
				}
				klog.Errorf("Error deleting %s '%s': %v", objType, name, err)
				return true
			}
			// 删除 ScaledObject
			scaledObjectName := fmt.Sprintf("%s-%s", serverName, server.Version)
			err := keda.deleteObject(&kedav1alpha1.ScaledObject{}, app.Namespace, scaledObjectName)
			if handleError(err, "ScaledObject", scaledObjectName) {
				return err
			}

			// 删除 HTTPScaledObject
			//httpscaledObjectName := fmt.Sprintf("%s-%s", serverName, server.Version)
			//err = keda.deleteObject(&httpv1alpha1.HTTPScaledObject{}, app.Namespace, httpscaledObjectName)
			//if handleError(err, "HTTPScaledObject", httpscaledObjectName) {
			//	return err
			//}
		}
	}

	return nil
}

// deleteObject 封装了删除对象的逻辑
func (keda *KedaAutoScaling) deleteObject(obj client.Object, namespace, name string) error {
	//1、先获取，再删除
	err := keda.kedaClient.Get(context.TODO(), client.ObjectKey{Namespace: namespace, Name: name}, obj)
	if err != nil {
		return err
	}
	return keda.kedaClient.Delete(context.TODO(), obj)
}

func merge(v1, v2 *v1alpha1.AutoScalingSpec) *v1alpha1.AutoScalingSpec {
	//如果 v1 是 nil，直接返回 v2
	if v1 == nil {
		return v2
	}
	merged := *v1
	if merged.CooldownPeriod == nil {
		merged.CooldownPeriod = v2.CooldownPeriod
	}
	if merged.Triggers == nil {
		merged.Triggers = v2.Triggers
	}

	if merged.Serverless == nil {
		merged.Serverless = v2.Serverless
	}
	if merged.MinReplicas == nil {
		merged.MinReplicas = v2.MinReplicas
	}
	if merged.MaxReplicas == nil {
		merged.MaxReplicas = v2.MaxReplicas
	}

	if merged.Backend == "" {
		merged.Backend = v2.Backend
	}
	// 返回合并后的结果
	return &merged
}
