package assembly

import (
	"gitlab.leinao.ai/application-controller/pkg/backend/assembly/autoscaling"
	"gitlab.leinao.ai/application-controller/pkg/backend/assembly/deploy"
	"gitlab.leinao.ai/application-controller/pkg/backend/assembly/istio"
)

type Builder interface {
	IstioBuilder() *istio.Builder
	DeployBuilder() *deploy.Builder
	AutoscalingBuilder() *autoscaling.Builder
}

type kubernetesBuilder struct {
	istioBuilder       *istio.Builder
	deployBuilder      *deploy.Builder
	autoscalingBuilder *autoscaling.Builder
}

func NewKubernetesBuilder() Builder {
	kb := &kubernetesBuilder{
		istioBuilder:       istio.NewIstioBuilder(),
		deployBuilder:      deploy.NewDeployBuilder(),
		autoscalingBuilder: autoscaling.NewAutoscalingBuilder(),
	}
	return kb
}

func (build *kubernetesBuilder) IstioBuilder() *istio.Builder {
	return build.istioBuilder
}

func (build *kubernetesBuilder) DeployBuilder() *deploy.Builder {
	return build.deployBuilder
}

func (build *kubernetesBuilder) AutoscalingBuilder() *autoscaling.Builder {
	return build.autoscalingBuilder
}
