package istio

import (
	appv1alpha1 "gitlab.leinao.ai/application-controller/apis/application/v1alpha1"
)

var builder *Builder
var app *appv1alpha1.Application

// func init() {
// 	builder = NewIstioBuilder()
// 	var num int32 = 100
// 	app = &appv1alpha1.Application{
// 		ObjectMeta: metav1.ObjectMeta{
// 			Name: "app",
// 		},

// 		Spec: appv1alpha1.ApplicationSpec{
// 			Gateway: &appv1alpha1.GatewaySpec{
// 				Host: "app",
// 			},
// 			Version: "v1",
// 			Server: map[string][]appv1alpha1.ServerSpec{
// 				"app": {
// 					{
// 						Version:  "v1",
// 						Replicas: pointer.Int32Ptr(1),
// 						ServiceMesh: &appv1alpha1.ServiceMeshSpec{
// 							IngressGateway: true,
// 							Backend:        appv1alpha1.Istio,
// 							SubRoute: &appv1alpha1.SubRoute{
// 								Weight: &num,
// 								Retries: &appv1alpha1.HTTPRetry{
// 									AutoRetire: true,
// 									Attempts:   3,
// 								},
// 								TrafficPolicy: &appv1alpha1.TrafficPolicy{
// 									LoadBalancer: 4,
// 									CircuitBreaker: &appv1alpha1.CircuitBreaker{
// 										MaxConnections:          100,
// 										HTTP1MaxPendingRequests: 100,
// 										ConsecutiveErrors:       5,
// 										Interval:                &metav1.Duration{Duration: time.Second * 5},
// 										BaseEjectionTime:        &metav1.Duration{Duration: time.Minute * 5},
// 										MaxEjectionPercent:      100,
// 									},
// 								},
// 							},
// 						},
// 					},
// 				},
// 			},
// 		},
// 	}
// }

// func Test_Gateway(t *testing.T) {
// 	builder.BuildGateway(&clientnetworking.Gateway{
// 		ObjectMeta: metav1.ObjectMeta{
// 			Name: "app",
// 		},
// 	}, app)
// }

// func Test_BuildVirtualService(t *testing.T) {
// 	vs := &clientnetworking.VirtualService{
// 		ObjectMeta: metav1.ObjectMeta{
// 			Name: "app",
// 		},
// 	}

// 	builder.BuildVirtualService(vs, "app", "127.0.0.1", app)
// }

// func Test_BuildDestinationRule(t *testing.T) {
// 	server := []systemv1alpha1.ServerSpec{}
// 	for _, v := range app.Spec.Server {
// 		server = append(server, v...)
// 	}

// 	dr := &clientnetworking.DestinationRule{
// 		ObjectMeta: metav1.ObjectMeta{
// 			Name: "app",
// 		},
// 	}
// 	builder.BuildDestinationRule(dr, "app", server)
// }
