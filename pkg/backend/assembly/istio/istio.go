package istio

import (
	"encoding/json"
	"fmt"
	"reflect"

	systemv1alpha1 "gitlab.leinao.ai/application-controller/apis/application/v1alpha1"
	"google.golang.org/protobuf/types/known/durationpb"
	networkingv1alpha3 "istio.io/api/networking/v1alpha3"
	clientnetworking "istio.io/client-go/pkg/apis/networking/v1alpha3"
)

var Selector = map[string]string{SelectorKey: SelectorValue}

type Builder struct{}

func NewIstioBuilder() *Builder {
	return &Builder{}
}

func (s *Builder) BuildGateway(gateway *clientnetworking.Gateway, app *systemv1alpha1.Application) {
	gateway.Spec = networkingv1alpha3.Gateway{
		Selector: Selector,
		Servers: []*networkingv1alpha3.Server{
			{
				Port: &networkingv1alpha3.Port{
					Number:   80,
					Name:     "http",
					Protocol: "http",
				},
				Hosts: []string{app.Spec.Gateway.Host},
			},
		},
	}
}

func (s *Builder) BuildVirtualService(vs *clientnetworking.VirtualService, name, hostSuffix string, app *systemv1alpha1.Application) {
	vs.Spec.Http = make([]*networkingv1alpha3.HTTPRoute, 0)
	for _, ser := range app.Spec.Server[name] {
		if ser.ServiceMesh != nil {
			if ser.ServiceMesh.IngressGateway {
				vs.Spec.Gateways = []string{fmt.Sprintf("%s/%s", GatewayNamespace, GatewayName), GatewayMesh}
				vs.Spec.Hosts = []string{name}
				if app.Spec.Gateway != nil && app.Spec.Gateway.Host != "" && hostSuffix != "" {
					vs.Spec.Hosts = append(vs.Spec.Hosts, app.Spec.Gateway.Host+"."+hostSuffix)
				}
			} else {
				vs.Spec.Hosts = []string{name}
			}
			if hr := buildHTTPRouteDestination(vs, name, ser); hr != nil {
				if ser.ServiceMesh.IngressGateway {
					hr.CorsPolicy = &networkingv1alpha3.CorsPolicy{
						AllowOrigins: []*networkingv1alpha3.StringMatch{
							{
								MatchType: &networkingv1alpha3.StringMatch_Regex{
									Regex: ".*",
								},
							},
						},
						AllowMethods: []string{"POST", "GET", "PUT", "PATCH", "OPTIONS", "DELETE"},
						AllowHeaders: []string{"*"},
					}
				}
				vs.Spec.Http = append(vs.Spec.Http, hr)
			}
		}
	}
}

func buildHTTPRouteDestination(vs *clientnetworking.VirtualService, name string, server systemv1alpha1.ServerSpec) *networkingv1alpha3.HTTPRoute {
	buildHD := func() *networkingv1alpha3.HTTPRouteDestination {
		hd := &networkingv1alpha3.HTTPRouteDestination{
			Destination: &networkingv1alpha3.Destination{
				Host:   name,
				Subset: server.Version,
			},
		}

		if server.ServiceMesh.SubRoute.Weight != nil {
			hd.Weight = *server.ServiceMesh.SubRoute.Weight
		}
		return hd
	}

	for _, http := range vs.Spec.Http {
		matchList := make([]*networkingv1alpha3.HTTPMatchRequest, 0)
		match, _ := json.Marshal(server.ServiceMesh.SubRoute.Match)
		_ = json.Unmarshal(match, &matchList)
		if reflect.DeepEqual(http.Match, matchList) {
			http.Route = append(http.Route, buildHD())
			return nil
		}
	}
	hr := &networkingv1alpha3.HTTPRoute{
		Route: []*networkingv1alpha3.HTTPRouteDestination{buildHD()},
	}

	if server.ServiceMesh.SubRoute.Retries != nil && server.ServiceMesh.SubRoute.Retries.AutoRetire {
		hr.Retries = &networkingv1alpha3.HTTPRetry{}
		if server.ServiceMesh.SubRoute.Retries.Attempts > 0 {
			hr.Retries.Attempts = server.ServiceMesh.SubRoute.Retries.Attempts
		}
		if server.ServiceMesh.SubRoute.Retries.PerTryTimeout != nil {
			hr.Retries.PerTryTimeout = durationpb.New(server.ServiceMesh.SubRoute.Retries.PerTryTimeout.Duration)
		}
	}
	if server.ServiceMesh.SubRoute.Match != nil {
		hr.Match = make([]*networkingv1alpha3.HTTPMatchRequest, 0)
		match, _ := json.Marshal(server.ServiceMesh.SubRoute.Match)
		_ = json.Unmarshal(match, &hr.Match)
	}
	return hr
}

func (s *Builder) BuildDestinationRule(dr *clientnetworking.DestinationRule, name string, server []systemv1alpha1.ServerSpec) {
	dr.Spec.Host = name
	dr.Spec.Subsets = make([]*networkingv1alpha3.Subset, 0)
	for _, ser := range server {
		sub := &networkingv1alpha3.Subset{
			Name:   ser.Version,
			Labels: map[string]string{"version": ser.Version},
		}
		if ser.ServiceMesh.SubRoute == nil || ser.ServiceMesh.SubRoute.TrafficPolicy == nil {
			goto APPEND
		}
		sub.TrafficPolicy = &networkingv1alpha3.TrafficPolicy{
			LoadBalancer: &networkingv1alpha3.LoadBalancerSettings{
				LbPolicy: &networkingv1alpha3.LoadBalancerSettings_Simple{
					Simple: networkingv1alpha3.LoadBalancerSettings_ROUND_ROBIN,
				},
			},
		}
		if ser.ServiceMesh.SubRoute.TrafficPolicy.CircuitBreaker != nil {
			sub.TrafficPolicy.ConnectionPool = &networkingv1alpha3.ConnectionPoolSettings{}
			if ser.ServiceMesh.SubRoute.TrafficPolicy.CircuitBreaker.MaxConnections > 0 {
				sub.TrafficPolicy.ConnectionPool.Tcp = &networkingv1alpha3.ConnectionPoolSettings_TCPSettings{
					MaxConnections: ser.ServiceMesh.SubRoute.TrafficPolicy.CircuitBreaker.MaxConnections,
				}
				sub.TrafficPolicy.ConnectionPool.Http = &networkingv1alpha3.ConnectionPoolSettings_HTTPSettings{
					MaxRequestsPerConnection: ser.ServiceMesh.SubRoute.TrafficPolicy.CircuitBreaker.MaxConnections,
				}
			}
			if ser.ServiceMesh.SubRoute.TrafficPolicy.CircuitBreaker.HTTP1MaxPendingRequests > 0 {
				if sub.TrafficPolicy.ConnectionPool.Http != nil {
					sub.TrafficPolicy.ConnectionPool.Http.Http1MaxPendingRequests = ser.ServiceMesh.SubRoute.TrafficPolicy.CircuitBreaker.HTTP1MaxPendingRequests
				} else {
					sub.TrafficPolicy.ConnectionPool.Http = &networkingv1alpha3.ConnectionPoolSettings_HTTPSettings{
						Http1MaxPendingRequests: ser.ServiceMesh.SubRoute.TrafficPolicy.CircuitBreaker.HTTP1MaxPendingRequests,
					}
				}
			}
			sub.TrafficPolicy.OutlierDetection = &networkingv1alpha3.OutlierDetection{}

			//TODO： 暂时注释过静态检查，后期确定有没有字段替代  ser.ServiceMesh.SubRoute.TrafficPolicy.CircuitBreaker.ConsecutiveErrors is deprecated: Do not use.
			//if ser.ServiceMesh.SubRoute.TrafficPolicy.CircuitBreaker.ConsecutiveErrors > 0 {
			//	sub.TrafficPolicy.OutlierDetection.Consecutive_5XxErrors = &wrappers.UInt32Value{Value: uint32(ser.ServiceMesh.SubRoute.TrafficPolicy.CircuitBreaker.ConsecutiveErrors)}
			//}
			if ser.ServiceMesh.SubRoute.TrafficPolicy.CircuitBreaker.MaxEjectionPercent > 0 {
				sub.TrafficPolicy.OutlierDetection.MaxEjectionPercent = ser.ServiceMesh.SubRoute.TrafficPolicy.CircuitBreaker.MaxEjectionPercent
			}
			if ser.ServiceMesh.SubRoute.TrafficPolicy.CircuitBreaker.Interval != nil {
				sub.TrafficPolicy.OutlierDetection.Interval = durationpb.New(ser.ServiceMesh.SubRoute.TrafficPolicy.CircuitBreaker.Interval.Duration)
			}
			if ser.ServiceMesh.SubRoute.TrafficPolicy.CircuitBreaker.BaseEjectionTime != nil {
				sub.TrafficPolicy.OutlierDetection.BaseEjectionTime = durationpb.New(ser.ServiceMesh.SubRoute.TrafficPolicy.CircuitBreaker.BaseEjectionTime.Duration)
			}
		}
	APPEND:
		dr.Spec.Subsets = append(dr.Spec.Subsets, sub)
	}
}
