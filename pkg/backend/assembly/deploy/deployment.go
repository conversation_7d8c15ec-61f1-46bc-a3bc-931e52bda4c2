package deploy

import (
	"gitlab.leinao.ai/application-controller/apis/application/v1alpha1"
	v1 "k8s.io/api/apps/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

type Builder struct{}

func NewDeployBuilder() *Builder {
	return &Builder{}
}

func (s *Builder) BuildDeployment(deployment *v1.Deployment, serverName string, app *v1alpha1.Application, serverSpec *v1alpha1.ServerSpec) error {

	labels := deployment.ObjectMeta.Labels
	if labels == nil {
		labels = make(map[string]string)
	}
	labels[v1alpha1.LabelApplicationName] = app.Name
	labels[v1alpha1.LabelApplicationVersion] = app.Spec.Version
	labels[v1alpha1.LabelAppName] = serverName
	labels[v1alpha1.LabelAppversion] = serverSpec.Version
	deployment.ObjectMeta.Labels = labels

	// Ensure the serverSpec.Template.Spec is properly initialized  # 设置终止宽限期为60秒
	if serverSpec.Template.Spec.TerminationGracePeriodSeconds == nil {
		gracePeriod := int64(60)
		serverSpec.Template.Spec.TerminationGracePeriodSeconds = &gracePeriod
	}
	deployment.Spec = v1.DeploymentSpec{
		Replicas: serverSpec.Replicas,
		Selector: &metav1.LabelSelector{
			MatchLabels: map[string]string{
				v1alpha1.LabelAppName:    serverName,
				v1alpha1.LabelAppversion: serverSpec.Version,
			},
		},
		Template: serverSpec.Template,
	}

	return nil

}
