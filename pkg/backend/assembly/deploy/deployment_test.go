package deploy

import (
	"testing"

	appv1alpha1 "gitlab.leinao.ai/application-controller/apis/application/v1alpha1"
	corev1 "k8s.io/api/apps/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

func Test_BuildDeployment(t *testing.T) {
	deploy := &corev1.Deployment{
		ObjectMeta: metav1.ObjectMeta{
			Name: "app",
		},
	}
	var num int32 = 1
	serverSpec := &appv1alpha1.ServerSpec{
		Replicas: &num,
		Version:  "v1",
	}
	app := &appv1alpha1.Application{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "llm-predict",
			Namespace: "llm-example",
		},
		Spec: appv1alpha1.ApplicationSpec{
			Version: "v1",
		},
	}

	err := NewDeployBuilder().BuildDeployment(deploy, "app", app, serverSpec)
	if err != nil {
		t.Errorf("Unexpected error: %v", err)
	}
}
