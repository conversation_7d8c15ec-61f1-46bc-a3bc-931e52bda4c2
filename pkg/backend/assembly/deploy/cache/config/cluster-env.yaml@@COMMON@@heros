#http协议类型
HTTP_TYPE: https
#websocket协议类型
WS_YTPE: wss
#主页地址
WEB_URL: hero-dev.cnbita.com

#域名证书，用于ingress使用，不使用为空
Ingress: 
  Domain: https://hero-dev.cnbita.com
  Host: 
  TlsSecretName:
PublicCert: 
  Cert: 
  Key: 

VncBaseURL: https://hero-dev.cnbita.com/desktop/vnc.html?path=websockify/?token=
#私有镜像仓库
Image:
  ImageRepo: https://hero-dev-image.cnbita.com:5000
  Domain: hero-dev-image.cnbita.com:5000
  UserName: admin
  Password: ENC(WqTujpR2+PAnb5QMQymRct0gXYHiUZxe+mHGN/xfhxi8O6tZnQcp0cNqzSUovqPC)

#GIT相关配置
GLOBAL_GIT:
  #gitlab地址
  URL: hero-dev-code.cnbita.com
  #gitlab端口，如果有端口则填写，没有为空
  PORT: ""
  #gitlab公共账号public_business_user授权Token
  ACCESS_TOEKN: gDHwzWWVwuyGSeA5amx2
  #gittea域名
  GITEA_HOST: hero-dev-code.cnbita.com
  #gittea域名对应端口
  GITEA_PORT: ""
  #gittea管理员授权AccessToken
  GITEA_ACCESS_TOEKN: 6c382ddd9aef30e65a271470e4123513bbdda53f

#对象存储相关配置[后期只用一个minioGW]
S3:
  #对象存储地址
  URL: hero-dev-minio.cnbita.com
  #如果有端口则填写，没有为空
  PORT: ""
  StorageType: s3
  # 访问账户
  ACCESS_KEY: LEINAOYUNOS
  # 访问密码
  SECRET_KEY: ENC(K2aWPj2/ANmtz26jCRDPtkkPzpSHlh2dEotXJihYh2hcLpzZjhiNgtMODTMDfmmrUu4E8WDDvpKKLFZjXB92eNN4/xBgLSSj7ekxY9Xk6I4=)
  # 访问方式
  HTTPS: true
  Vendor: MinIO

FileSystem:
  #MINIO网关地址
  URL: hero-dev-miniogw.cnbita.com
  #minio端口，如果有端口则填写，没有为空
  PORT: ""
  StorageType: filesystem
  # 访问账户
  ACCESS_KEY: LEINAOYUNOS
  # 访问密码
  SECRET_KEY: ENC(K2aWPj2/ANmtz26jCRDPtkkPzpSHlh2dEotXJihYh2hcLpzZjhiNgtMODTMDfmmrUu4E8WDDvpKKLFZjXB92eNN4/xBgLSSj7ekxY9Xk6I4=)
  # 访问方式
  HTTPS: true
  #路径
  PATH: /user-storage
  Vendor: SandStone

#集群配置
CLUSTER:
  #集群IP，默认为HA VIP地址
  IP: ***********
  #API端口，默认3master 端口为16443
  API_PORT: ":16443"
  #验证Kubernetes API server证书的CA证书
  CERTIFICATE_AUTHORITY_DATA: ENC(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)
  #客户端证书，用于向API server证明客户端的身份
  CLIENT_CERTIFICATE_DATA: ENC(jU6HScgscMGQzhAj7VtX3DePY43aKwiwx8YgFZ8o3eOI2n52AeTpNrL7+ZwsmS2yWLGQN1yNoP/Kw0IrFTBrDvQf0c6p+2CK6zbbCiPO/LF0wlWJ/4GHyGpfEx701er66/8KOoRzcY7ub6S1nuEK0bwbKhV2YEbk5dZ4y7pC7/JfVFprfiLoa4QMkY7Tu8CT7cTmQXhy+HKzel6/7LtdqL4iZKEcC7/X09rkEow9AaOSBYv1rUs/jIkUUk+OOTtKDUIkcqGiNKzNoKQOALEsXLPetu394BJmZ2/bYsZFi3HtaXxs9Zg04RkuvcQFLRRB4GvtGzHru1Cyn1gBHzX8VWz7eFn0fNFkYwwpKtpaTW+oKd+tmhO5wOSNo4v+diYMRb7iJKiOHhO7w6pURMjIIaIMycPva1qsb8UadRy2DvA0USWluSNW+kfJvGXmu8OsxEumwzVpalO35JfSHeibL8goLTrfUnf6tyzLUPc3OWVrgrmxyiIHwcT5ALq9H0QCryYd2+YBuuc+V/SB0WR9KvVYQsb8CPC63lyy3rrF6SG3Ot0byhz5MRVZMxdadapAdtYM52V/KF08UblUr+zuZWOoEEc4jHFjGW2h3vPwckPpJU1iPPxJ5GGdbY7QBaWbdzFFRrJVoi075W4aYrnr846/AYcmD14q6SsXjoGZ3ZXW6lHkZw38zV8l5HjBbNUlAVpnn2H+VMt5SLGdOUXhGB4cmOD5o6OAvHCfSnnSqgA2gapwOVebJRcuVs0OYE/lvdLbh00MpfxyFqIvu73+5LQIOKIRkyRQ5utgGzfxDFVkP1Mcr+ghVcdZFn6fh7va6GNgln6pse3LqLaZDlU2aFjThPbYwW02POMJx3RGeXnQUWpDg3NemM2UtEoC07UUIAIupuMyvwnV7YDayNZv4nJx46Q9yDDDSJA/Ebm9/Eddb/NEHuklSGOMYLHY8z7AxQsrCGyBC4+hnH9B8s31X+HTWXq96sSPTCotsFre8S98joIcPWFJM/Bc95v/NN1+mq4CB0VcMbTRnQNQoy2MzKLmKSHg0gb9k9Ackvf2nrwO6UUSJhSGiOmEGNI4H0GUg8sLgiVvTjmyU5v52ZjzzA5SiSJJcvc3jfbWaufNTvARR9mcP3b21ju1COg+bVf3x+R8J4rIdMZGaMQkf36ZGhBgJDiOkpIEa7eBjLcnOB8/VCZbF4ggrh12FQXe38yttrgiA/Oj2qRAZzHgN7XKznDl5E8XcnS0OuQxp6H70jlktaQvI9ZqLiOiSm9uqNZ7/HJtMaL7EgfcFHLBa1RbcT5RQ8F133GZgY3bbjgBIVTrWGDNJWH4foSrYSd75rxeUsY43uinSUQsM0rz6s4Jdkjt2ddY1/hwQJQ5RvZFyNkQSkGL/ThzHHlp+tfDPgEsHfXz4jC7msUjc+hv2QBKbCl3R5qgsOe689EZr/jXEm2RrtE1SWmF3k2pNWlk+tGG/+dEx56f4u+NDg6A71ky0bF8ghStF0RqLrAfGMO4hVcAcwYGTEaLwxCPZsd3r2w5SIKVy/GkO9rtUDMrqmC/Hun8lrIrjkq7NV/nnqvM4edGkUHVrsx9udqYuS0z0C5cws0/Y1BxxHEKbTWfZOWjwSjnUBs1elo6d4aVtGl7Q1R3X8SydqyUQoWE27xNXIImgK7S+khi45a+FfrYeg50+eH5NRF+Q5kD90klqlsT6dbaKcEuNDqCEE/M4z31dkTsj1WM2qHxABXYQpa+l81B/m8usE3a8pmoq6rcnasKT3OLJ+b8L00wwBT8q+XvEeSSB+1G8fFRIt5qFwRkyqpmBN007ouIMhZm6RHl977F0ThXiEtBSeStq6ar0FeOW0Os1bF0H2G//7sAnrYQ9uj9X57CyC9vFOfLUNU4jhAdZbV9XKfdl1qDT6nyu5BXS/xRpm5lV7c5MNQgdKh90Xh/mYsRjGH98kULfl4ucKYFeHwfiUwl01+4OCTwG2MTYUKU+ggy5B1KMB4v/vdOWaLFIV5KxFXiHUdXuiwXdfLc4ZNWmkw75ZEtQbJ1sqLk9zYqEuBM2VAEhgip+7TRPIG3cwrEgjBn4XcZ9ai512zcxZw=)
  #证书对应的私钥
  CLIENT_KEY_DATA: ENC(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)

#集群配置
CLUSTER_K8S:
  #集群IP，默认为HA VIP地址
  IP: ***********
  #API端口，默认3master 端口为16443
  API_PORT: ":6443"
  #验证Kubernetes API server证书的CA证书
  CERTIFICATE_AUTHORITY_DATA: ENC(JoP2EcHJwguIVVOR8pdUuFLT6iNdzm/cWUwd4G4H2t8pvbqIB3k4XO7KBM9IykD/AvEH8nsij04AITVPY7ZfL5Iq545G17+7z8JvXbY2ue/dPUPcRXu7LiWxG8xAvl4pfIoluqVf1I9M9U9TR0e1tqFYdIJ7q12iEpcgRmRqgJb9M+H61PepegMZMmmK/NbVFO5Cq+dhZj4Upw7QkpHZb444zuGHD+U6IA9QR+mgJHQ2GiKVqAFrNqfPSJ4P9Hgz4n932udK8EPsLwZHjzEfHwvh9E8P+JGrujhkC97yUhwjEn8uxELbYg2vODTY8D28EWEjlTX625lTHtXKnz+D1QQAuEcuKRkk18ij0DSjB5g5YYdHKnvXXaVPeeUJ4GHt5cDWtSwAnMIuipf6H5wNln+5hgbY4wFcwoNj5ntsxzubqUJuqFU5GhYLNtPiVOyu6cHbTGjx5JpCMY3Lx18jbRa/+36iP7/fOc398t5icI/BA3HSEteQc9Rx4vRSRpX+wzEngiRnmbJD9e6qVZJTNqeUguAvlRYORAUVGgxjkw20fRZI4t/4tm0TrAxPC8MOCytoA2ep+JY9bI31aoA6yoAE/YjZFgtw09mwCw07nmKeLTuKceP4fBnVb9hL4p4KBBY5+lQLh31blSkeKCvtfxpOsnLGBcEuOSfnHTnzpoVyNZAwbM4vVbwekfBbOtpcQOoduEr5r+8kyAk48nMiNcc8MRjPDEpStmlyrqJKD+vLTj0pHJH6tsjCuLA7btM59/YNPk3c7ByZi8k26/9hljPB8CHHnuvW15ZsuSSrgp3h4Dyw4EIlYgXoKyfU4lgqxEhZVzRk1TWhYG3N/nGFX182cvACEYqX4VNTAfaLih1NUQuw4jIDgo3RrzeMBinj8MbbctnEutiTDGojFx9TzyS8FXGqE7xbW++xvoBeuKj+SBelRH0U4fM2uWD4hSCWB+RTh7sXUvxx0emwb8NYcKjq8MApzEDB7TCaO6viyYvYEzDvZVWGV9kieCC+CGj0QOYLFYKDaidKNguFuvJKCQvlUX+/+dd8/5mqGy0TzRkx6tbYt1nNYaEMQEuAe6z55wVlJUxk9ozmn7DGtU37A34G71XCf9SSxGKpGELBJuTwra4stTahVOSB/08MbkKsxMDwFIFyBf0jvYEwY2M9PzT61fLkYiNEdy0WBn2Tib7gTDJT0tMwR3tc5mQao08fMzijsCV6ecx/OIANaQjQ6fauh2bn6YhihLzKtmg09foCRbtXporN0f/34BaHhrQQMvHBYLNuRulP023OFsmmqxAneX485UOQjPfyKfxKn6ie6jUEWCQzctkbBvEBHtj2kN8oyjSXncDTRZqV/g6Kth6VXUApPXPfMBaqNeR2H62xr34rhX3XsVSb9vtHuWlAsR5mGncbnzC9JfXw1TQBAsPlrvbCXlgojNOoZ5Kkhr5DjOvgPDq8taq0f9wClZiVYduVrVgiFe8dvl9PcJAe32y+MsSMIvGDhcudtEPjWhBrQSxYeuCCNAFXmxYJV078M0pud2LHNon18lv8hJHqhiES5TTLMltIV4ya8Lm9+jQDsr274M/XrYaFyRpXn1plsvDIq3yB8wctTJIQp/b4JmZwmep00bxATeuywxTdQvrp5YS3xm/nI/wDFRzDl5UQHnnxGqll3+YyJGlfwjL/QPNGypehs67cIrJ37aOBV71BL3T142yHHS7V9EVuXYvvpbfBnGh+ZfB9qL17kut9WYJ7r0LDMRwtPdTXYHkqqG48YGx91BN/gJgT87Wi5sASXA2+UzuGsmcbK5sNgI3OJywYSNZ85bd7oYGruYP7OtNw1ZXZ2vHPiiSkmuVzHELNPczn45E+GxRLiL2lVVZcLYZFSt7XoNA7N5j+UnoEIHXfMB0WnY1mbL1L12B/V2RJATkvvEfrCPX/SLF8Gs2gLaTmWt6PHruvhmwPJVDBszLPLDhpprM6N8JlhBPEYy8AfTyCyVgET5Dz65umh6J8ZyBfOxLpZlmDs4X2UnQ2HeY=)
  #客户端证书，用于向API server证明客户端的身份
  CLIENT_CERTIFICATE_DATA: ENC(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)
  #证书对应的私钥
  CLIENT_KEY_DATA: ENC(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)
  TOKEN: *************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

#hero系统服务sa-token
HERO-SA-SECRET: ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

# Redis数据库信息
REDIS:
  # 数据库用户密码
  PWD: ENC(aYYfNR92zYwEE96UkMdWoQnxxoHev2vuaY5+bXswMl4dQ23MD7K4T2XegckZBemX+WwchP1KJhX2hMzW3Y5lhw==)

# Mysql数据库信息
MYSQL:
  # 主数据库地址
  MASTER-URL: my-cluster-mysql-master.basic-component.svc:3306
  # 从数据库地址
  SLAVE-URL: my-cluster-mysql-master.basic-component.svc:3306
  # 数据库用户名
  USERNAME: root
  # 数据库用户密码
  PWD: ENC(tstrv2XO4ulHBLh39ReIxwXAO7wswX99e98d8uNKoAmfsiTWEFt2hpwjQ31p5yjd8CZu/YAQNJPxwTeY4i8Jwg==)

# Mongodb数据库信息
MONGODB:
  # 数据库用户名
  USERNAME: root
  # 数据库用户密码
  PWD: ENC(gdeNdfj1tnf046HE/rpWk036TeGC3d4W01A4BzIx+d3ZQl+cfexGzDBh5MEC5nSl)

#邮件服务器地址
GLOBAL_MAIL:
  SMTP:
    # 邮件服务地址
    HOST: smtp.163.com
    # 邮件服务端口
    PORT: 25
  # 发件人邮箱
  FROM: <EMAIL>
  # 发件人密码
  FROM_PASSWORD: ENC(RsO4x97G2oACEwsE07yZsHyLJTVW8b1maPxdVS0iwriAdjJ398Q4ma1ReEHuonbbYSpOPODKwpyCfecZkCCLjg==)
  # 发件人名称
  FROM_NAME: ai_traininghub

#短信服务商配置
GLOBAL_SMS:
  # 短信签名
  SIGN: 彼塔社区
  # 短信AK
  ACCESS_KEY_ID: LTAIpD4C2q6aI83Y
  # 短信SK
  ACCESS_KEY_SECRET: ENC(03U3K88JyfpTtJPZKhK0GknWu9eiD+7m1Dx/dcLwzGKkv56e66TmJ0FDqk/VEukdO5RriRBROf9Y4ec60O3HnQ==)
  # 短信区域，阿里云需要
  REGION: cn-hangzhou
  # 短信应用ID，腾讯短信需要，因为目前用的阿里云短信，所以值不需要关心
  SDK_APP_ID: xxx

# 应用市场
SERVERLESS:
  # serverless服务体验地址生成规则,%s业务代码中需要替换成sys_name
  EXPERIENCE_URL: "%s.hero-user.svc.cluster.local"

# RSA加密算法密钥管理 - 苏奇亮
RSA:
  # RSA公钥
  PUBLIC_KEY: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDZrXkoArhYaCBpEh1k+mGdPO9W3okfbAVE+30jGixnyn7tqkymZY3NrVMZH7Rhhtv0+gtm+HXDFGdypCcvF/qXhm+N6PpMXWESwJxJclT3nFWxIdKpfC8G7veuXCwzF0NOZenoqNDB1MWMSf7lFuAGb45WLKhwfoNohacbZlYqHQIDAQAB
  # RSA私钥
  PRIVATE_KEY: ENC(UAw27QYRs2IGpzZuN/teItx+YDZUflU6NSK7N+o8pM5WrN8W5XMVkCllMxFqg5ZPHC5ZJdk+CwT0CzDfrZszaCwe710NSN9hrAk07k0RsQmM3xyB94lJM1SmjZmELRfcjBDPC/ePkO3APdzPlTlmrnazVYUETON/B9NarDlcig6eo4NdNOeIEdKXpFTvARKPrp/6a/6h2NDkjG1kYWamvAUUvsZw6LteRp8eX3oecZDxVIB1OapPs0BG+Vvs2JYrwRkpjtAreWVFPmPygBQNdWvSNgjuSAIstjlGiTtF94aT8BqIPmzXD4arY1de+U/q9Sb9AWIPqEoiLJNNpD18Xw1sF1x3QFhQkIUWPfBKIw5uqsDyliqHGDgrVTRYXaWyHpS+e3XSoQP+G/3Y3Gf9e25Fuqa0+1VY2wB53bpYhNvCVos+qVcL8x6IXlSKCPqK+Jrg46CM4cCsqiknBbNwAupbhqj+L1+IuOVBQNvYOw5A85djAhakwklOjYrgs9FqCB48Zf5n0jMTE+4JEkAXKKkZI3Bh4Dd0MYJmZbj3FnH4+Nh1/05ryHVGtiMQod4GzfbS1CEuO8d7apMCxruRDabLoU65kYUTXa5gJ16s1cSQuk5WeUi5XL3w3hZUQbFiPH37hws0BX0gHK9IG5Ur+TsuGjTjq4z85TLAow/VHSvzTCDksmut8NZsjw8faj/5U0LJP+iUD9oFbpdEuYxTLP5PRRjJ/emfN/fo4muEpm78SWfrwppYSQMfhRuwt307zh6mMk8vZ9T+mVIIqT6ZVtwCxlPP7GaZf3gBHk5Kx6wwtXWV308+EfbW+7pLRN9nDvc0u3j5FdncmqO60qXTF6lkT3XzzweBt62zCsiDAOQ5Fb+JyWfNTohNNdwkzGcsw1h4BqfVB8Vl5H130LjqY09HVUKYxQG61enwuaDH9YAVuzAOXYaOUat1sQ99cr4UYExPYcokABehJEJuOP/6ZsDBg4mf53fgzR9FJvo75K9qqmZlh6nqzmSn49yJpgpvjlx2Q2kIkwMKKs1y3TttsjNVNq9F6K3VxbfAbCshQ1rR+9ZURc9RELwEVS9Aotb/OthfnRy5hOfWrCiGMtaatyMPGyAyWlpD02pAAJwT9hmmLEALBcA0TA6JeX7iVj7ks3KkAY7c5+M/rDMLgHbIwFKB5yXHZd5pbQxhtfNS5xU=)

# 支付相关配置
ALIPAY:
  # 网关地址,不需要加/gateway.do，这是新旧SDK的区别，切记
  # https://openhome.alipay.com/develop/sandbox/tool 沙箱环境下的支付宝需要单独安装
  GATEWAY_HOST: openapi-sandbox.dl.alipaydev.com
  # 应用识别码
  APP_ID: 9021000138682740
  # 商户引用私钥，填应用私钥，注意是应用私钥，不要填成公钥了
  MERCHANT_PRIVATE_KEY: MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCyl5Kd4N9lj43t3jpkb4pVBk0Iakc/n07kxEAIWiYlBd/YBTytPwwxE+EIhNMoKP0SqVBkaCTdbkT7LOES9tIaTPrG0EAmS5eRNTnRpnb89LWo3oJvPsBKewiGeO4cEVb1GnWHocABux52GH58mXtoOLcfiz8//GAFwnhOkM5VGkRVoSrmYTPPaLiLZanVWmjKJKyxUv7E4gPg/omHw2tg+Z7rf6QTVXWXCLk6tFUXJSMOxG8npr2I6eAJOupuKs0ShTH3Ur8FcFwwgscSP4CgnfPYC+58tab5H8xtUuGkoa+WWvcFKgopoEeEZZrPAPHgN8bl4tUFHry/RYvUFgylAgMBAAECggEBAKnYOsjJDswwrUMWQKQ56+4WkvY41M+t+0LXXSpUSciWAJ8Zw+3jcv3NsQr//y1HZf70ZQ9IeB7aeuIBeYAmmgQHR8wCS6/f3iJr4zP+Q1UoiQczrgqMPCXZQm9Y3OJ2nyN06Wd3Lfq3cS3Q96IZ+WJR5d4q+JkqtEtl+jKFxqTpQm10j1+T8KsjAq3JBYjbRkAHwKdSjLz5N6NVuEwsVyVHGuUMsaY394ncgLUA1VW25aOlIC5jq1+Ur3PpTqfe4f0Qrnaod8p0kBbSMIfi+cjLnIJ1dOHhvJrO8DZeXpsH+dkgjJw+naU8Pk2DUDeAxfOvz6r/w6ng1qVKgpMSpoECgYEA2IyKL/DMqaWHunnQD+KWy6zyeUqAraqc3fBvkDT9rb32apS3D4gsUaX8Fz4Xq2TR7Z29nWFOzKB3SrqtQ4wisWz/jK87hdmXfGxaXEeneEkrEFHto/PimqOYhqyjZq1aqQCXReM9LLMuv+Cq8fO3/oiicD2fNo8yukEQCEsQ0WECgYEA0yDKVVl/uPiJ0pB0O1XmfjmydoDgu8GrlFGgYNu0lDDdFEKC4BUkIWInX54pKYpRfH8sYtponBb86kkuj1KEmlnVMvfP/dHynelFWzIE+14LKPZ5vYXilZiVRTJpeoIkHy31DixqH7YGqHI1SlJ25Mq8AxTQ5swG2FrfaV1jDcUCgYEArNSuTcjwb83PYENsjeEY3YIxHCEcRn9PJ8uIHx5MAWkFzNbYrCrQxHGnm51RItgavH0TIxLQ43yG+Tu4oZqs7dLCuzP4W8Hop8OK8u+1d8ld926MLjiueSFs0L/kwkRFgXEZSwwsfQqdm0Li7KzKAvpmOxLTiaYMpJ7c/uF6kMECgYAOQ8zn72Ad000cmP4BdFBafvN75kFI7lD1eH5pjO6qhJKrZ8MH+OW0C7m1UC5fzCStULpjA3bF9RepjMBElsK22xL4mp3G/IRz/urcuohzo2sFnYGkOIOSr/iF8F4IFPrz0FsaVg3ljprsjqrjFqdJ1hjV7HYg4fAkdh9aMyEaMQKBgEqb86bhhLDE7tLeQ+V7F/2qITC1DJv8iaoIkLDufxOzok7Pt8ST7rvb5DPUx+o6FtUFwqwg1zuJjlzzoODWoRMOx75f1p+KzjifKccbZQJQuIZ9ojjb3P9smgh6xseZ5+PrUcGqsAXjKAuVLPoFCsxc270zssYrvMXFb8Y6A2qu
  # 支付宝公钥 填支付宝公钥，注意不是生成的应用公钥
  ALIPAY_PUBLIC_KEY: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAlotf/OmfWm9Jgk/QZT40xyLxsHBAdkE9E6aCeP/k+2xmYPBG1mtp7PTFg/cHHtdQ3rcFgKxQFDWhPHhxDMDQoeoeoXQ60ikJYzHb5CCZKGeSfHlqKcpumJTXYdu/jdh68Bb1MzSoWbLE6hzMEtiQGaFCc+8TyUP08ou6c4v8MER7LX0snIEmToKu25fb+TGAFN8n5bklVwS7i113nEVKIJmbbhxG/Bb+XXQPlr7f9OlkqmAEA65Jr6zyAj+TCchDSLV03k2isPRloxuIQ2qlPszgQFEek5FJb1EWol3ZCo8Qt9YnBkGvxH+LAxFqYBb3l5Vu1vE890/tInJM37YxOwIDAQAB

# 微信公众号配置，每个环境不同
WX:
  MP:
    APP_ID: wxe5e17bd4401794da
    SECRET: d4e7493e4df435657a0024cb0d059987
    TOKEN: bitahub
    AES_KEY: bitahub

# 集群业务配置信息
CLUSTER_INFO:
  # 集群是否多云类型：false-单机群，true-多云多集群
  HUB_FLAG: false
  # webhook配置-告警使用
  WARNING-WEBHOOK:
    # heros：单集群 clusterhub：多集群(主控)
    APP-DEFINED: heros

#GRAFANA-TOKEN值单集群用Bearer ${HERO-SA-SECRET},多集群用Bearer ${CLUSTER_K8S.TOKEN},主要用于网关路由转发
GRAFANA-TOKEN: Bearer ${HERO-SA-SECRET}