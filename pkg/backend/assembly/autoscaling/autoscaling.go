package autoscaling

import (
	"fmt"

	kedav1alpha1 "github.com/kedacore/keda/v2/apis/keda/v1alpha1"
	systemv1alpha1 "gitlab.leinao.ai/application-controller/apis/application/v1alpha1"
	autoscalingv2 "k8s.io/api/autoscaling/v2"
)

const (
	PrometheusType    = "prometheus"
	promServerAddress = "serverAddress"
	QueryKey          = "query"
)

var promeSQL = map[string]string{
	//"http_requests_total": "sum(rate(http_requests_total{app=\"$1\"}[2m]))",
	"gpu_util":     "avg(DCGM_FI_DEV_GPU_UTIL{exported_pod=~\"%s.*\",gpu=~\".*\"})",
	"gpu_mem_util": "(avg(DCGM_FI_DEV_FB_USED{exported_pod=~\"%s.*\",gpu=~\".*\"})/(avg(DCGM_FI_DEV_FB_USED{exported_pod=~\"%s.*\",gpu=~\".*\"})+avg(DCGM_FI_DEV_FB_FREE{exported_pod=~\"%s.*\",gpu=~\".*\"})))*100", //利用率   DCGM_FI_DEV_MEM_COPY_UTIL
	//"http_requests_total": "sum(istio_requests_total{destination_app=\"%s\",destination_service_name=\"%s\",destination_service_namespace=\"%s\"})",
	//用于计算最近两分钟内的 HTTP 请求速率
	"http_requests_total": "avg(rate(istio_requests_total{destination_service=\"%s\",destination_service_name=\"%s\",response_code=~\".*\"}[2m]))",
}

type Builder struct{}

func NewAutoscalingBuilder() *Builder {
	return &Builder{}
}

// BuildScaledObject 注意 keda校验，如果只包含cpu或内存，则不支持minReplicaCount为0
func (as *Builder) BuildScaledObject(scaledObj *kedav1alpha1.ScaledObject, app *systemv1alpha1.Application, serverName string, server systemv1alpha1.ServerSpec, prometheusAddress string) {
	// 构建 ScaledObjectSpec
	var minReplica int32
	stabilizationWindowSeconds := int32(480)
	//开启弹性伸缩的话
	// if server.AutoScaling.Serverless == nil {
	// 	minReplica = *server.AutoScaling.MinReplicas
	// }

	scaledObjectSpec := &kedav1alpha1.ScaledObjectSpec{
		ScaleTargetRef: &kedav1alpha1.ScaleTarget{
			Name: fmt.Sprintf("%s-%s", serverName, server.Version),
		},
		// CooldownPeriod: server.AutoScaling.CooldownPeriod,
		//IdleReplicaCount: server.AutoScaling.MinReplicas,
		MinReplicaCount: &minReplica,
		// MaxReplicaCount: server.AutoScaling.MaxReplicas,
		Triggers: []kedav1alpha1.ScaleTriggers{},
		Advanced: &kedav1alpha1.AdvancedConfig{
			HorizontalPodAutoscalerConfig: &kedav1alpha1.HorizontalPodAutoscalerConfig{
				Behavior: &autoscalingv2.HorizontalPodAutoscalerBehavior{
					ScaleDown: &autoscalingv2.HPAScalingRules{
						StabilizationWindowSeconds: &stabilizationWindowSeconds,
					},
				},
			},
		},
	}

	// processTriggers := func(triggers []systemv1alpha1.ScaleTriggers) {
	// 	for _, trigger := range triggers {
	// 		scaledObjectTrigger := kedav1alpha1.ScaleTriggers{
	// 			Type:     trigger.Type,
	// 			Metadata: trigger.Metadata,
	// 		}
	// 		if trigger.Type == PrometheusType {
	// 			//keda的prometheus的类型不支持利用率Utilization指标类型
	// 			scaledObjectTrigger.Metadata[promServerAddress] = prometheusAddress
	// 			if queryFormat, ok := promeSQL[trigger.Metadata["metricName"]]; ok {
	// 				var query string
	// 				switch trigger.Metadata["metricName"] {
	// 				case "http_requests_total":
	// 					service := fmt.Sprintf("%s.%s.svc.cluster.local", serverName, app.Namespace)

	// 					query = fmt.Sprintf(queryFormat, service, serverName)
	// 					//query := fmt.Sprintf(queryFormat, app.Name, serverName, app.Namespace)
	// 					//scaledObjectTrigger.Metadata[QueryKey] = query
	// 				case "gpu_mem_util":

	// 					query = fmt.Sprintf(queryFormat, serverName, serverName, serverName)
	// 				default:
	// 					query = fmt.Sprintf(queryFormat, serverName)
	// 				}
	// 				scaledObjectTrigger.Metadata[QueryKey] = query
	// 			}
	// 		}
	// 		//对于cpu type ，如果是利用率的话，因产品设计是0-1的数值，keda填的需要*100
	// 		//triggers:
	// 		//- type: cpu  #cpu利用率
	// 		//metadata:
	// 		//	type: Utilization
	// 		//value: "50"
	// 		//if trigger.Type == CpuType && v2.MetricTargetType(trigger.Metadata["type"]) == v2.UtilizationMetricType {
	// 		//	//把字符串转成float 再乘100
	// 		//	floatValue := cast.ToFloat32(trigger.Metadata["value"])
	// 		//	multipliedValue := floatValue * 100.0
	// 		//	trigger.Metadata["value"] = cast.ToString(multipliedValue)
	// 		//}
	// 		scaledObjectSpec.Triggers = append(scaledObjectSpec.Triggers, scaledObjectTrigger)
	// 	}
	// }

	// 处理 AutoScaling 和 Serverless Triggers
	// processTriggers(server.AutoScaling.Triggers)
	// if server.AutoScaling.Serverless != nil {
	// 	processTriggers(server.AutoScaling.Serverless)
	// }

	// 构建 ScaledObject
	scaledObj.Spec = *scaledObjectSpec
}

//func (as *Builder) BuildHTTPScaledObject(httpScaledObj *httpv1alpha1.HTTPScaledObject, app *systemv1alpha1.Application, serverName string, server systemv1alpha1.ServerSpec) {
//	// 构建 HTTPScaledObjectSpec
//	httpsScaleObjectSpec := httpv1alpha1.HTTPScaledObjectSpec{
//		Hosts: []string{
//			fmt.Sprintf("%s.%s.svc.cluster.local", serverName, app.Namespace),
//		},
//		Replicas: &httpv1alpha1.ReplicaStruct{
//			Min: ptr.To[int32](0),
//			//	最大副本不指定的话默认是100
//		},
//		//TargetPendingRequests:不指定的话默认是100,代表http请求数
//		ScaleTargetRef: httpv1alpha1.ScaleTargetRef{
//			Deployment: fmt.Sprintf("%s-%s", serverName, server.Version),
//			Service:    serverName,
//			Port:       server.Template.Spec.Containers[0].Ports[0].ContainerPort,
//		},
//	}
//
//	// 构建 HTTPScaledObject
//	httpScaledObj.Spec = httpsScaleObjectSpec
//}
