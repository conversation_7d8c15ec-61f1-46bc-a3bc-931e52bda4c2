package autoscaling

import (
	"testing"

	kedav1alpha1 "github.com/kedacore/keda/v2/apis/keda/v1alpha1"
	appv1alpha1 "gitlab.leinao.ai/application-controller/apis/application/v1alpha1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/utils/pointer"
)

func Test_BuildScaledObject(t *testing.T) {
	obj := &kedav1alpha1.ScaledObject{
		ObjectMeta: metav1.ObjectMeta{
			Name: "keda",
		},
	}
	app := &appv1alpha1.Application{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "llm-predict",
			Namespace: "llm-example",
		},
	}
	var num int32 = 1
	serverSpec := appv1alpha1.ServerSpec{
		Version:  "v1",
		Replicas: pointer.Int32Ptr(1),
		AutoScaling: &appv1alpha1.AutoScalingSpec{
			Backend:        appv1alpha1.Keda,
			MinReplicas:    &num,
			MaxReplicas:    &num,
			CooldownPeriod: &num,
			Triggers: []appv1alpha1.ScaleTriggers{
				{
					Type:     "cpu",
					Metadata: map[string]string{"Utilization": "50"},
				},
			},
			Serverless: []appv1alpha1.ScaleTriggers{
				{
					Type:     "prometheus",
					Metadata: map[string]string{"http_requests_total": "20"},
				},
			},
		},
	}
	NewAutoscalingBuilder().BuildScaledObject(obj, app, "keda", serverSpec, "127.0.0.1")
}
