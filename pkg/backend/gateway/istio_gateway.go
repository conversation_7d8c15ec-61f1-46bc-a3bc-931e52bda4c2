package gateway

import (
	"context"
	"sync"

	"gitlab.leinao.ai/application-controller/apis/application/v1alpha1"
	"gitlab.leinao.ai/application-controller/pkg/backend/assembly/istio"
	networkingv1alpha3 "istio.io/api/networking/v1alpha3"
	clientnetworking "istio.io/client-go/pkg/apis/networking/v1alpha3"
	k8serrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

type IstioGateway struct {
	sync.Once
	client.Client
	*istio.Builder
	*runtime.Scheme
}

func (k *IstioGateway) Configure(app *v1alpha1.Application) error {
	if app.Spec.Gateway == nil {
		return nil
	}
	gateway := &clientnetworking.Gateway{
		ObjectMeta: metav1.ObjectMeta{
			Name:      istio.GatewayName,
			Namespace: istio.GatewayNamespace,
		},
		Spec: networkingv1alpha3.Gateway{
			Selector: map[string]string{istio.SelectorKey: istio.SelectorValue},
			Servers: []*networkingv1alpha3.Server{
				{
					Port: &networkingv1alpha3.Port{
						Number:   80,
						Name:     "http",
						Protocol: "http",
					},
					Hosts: []string{"*"},
				},
			},
		},
	}
	if err := k.Create(context.Background(), gateway); err != nil && !k8serrors.IsAlreadyExists(err) {
		return err
	}
	return nil
}

func (k *IstioGateway) Name() string {
	return "istio-gateway-1.19"
}

func (k *IstioGateway) Inject(app *v1alpha1.Application) error {
	return nil
}

func (k *IstioGateway) Setup(client client.Client, scheme *runtime.Scheme) {
	k.Once.Do(func() {
		k.Client = client
		k.Scheme = scheme
	})
}

func (k *IstioGateway) Clean(app *v1alpha1.Application) error {
	return nil
}
