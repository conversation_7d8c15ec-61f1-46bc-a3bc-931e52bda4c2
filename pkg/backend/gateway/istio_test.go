package gateway

import (
	appv1alpha1 "gitlab.leinao.ai/application-controller/apis/application/v1alpha1"
)

var istioAdapter *IstioGateway
var app *appv1alpha1.Application

// func init() {
// 	var scheme = runtime.NewScheme()
// 	utilruntime.Must(clientnetworking.AddToScheme(scheme))

// 	app = &appv1alpha1.Application{
// 		ObjectMeta: metav1.ObjectMeta{
// 			Name: "app",
// 		},

// 		Spec: appv1alpha1.ApplicationSpec{
// 			Gateway: &appv1alpha1.GatewaySpec{
// 				Host: "app",
// 			},
// 		},
// 	}

// 	gateway := &clientnetworking.Gateway{
// 		ObjectMeta: metav1.ObjectMeta{
// 			Name:      istio.GatewayName,
// 			Namespace: istio.GatewayNamespace,
// 		},
// 	}

// 	objs := []client.Object{
// 		gateway,
// 	}
// 	istioAdapter = &IstioGateway{
// 		Builder: istio.NewIstioBuilder(),
// 	}
// 	istioAdapter.Setup(
// 		fake.NewClientBuilder().WithScheme(scheme).WithObjects(objs...).Build(),
// 		scheme,
// 	)
// }

// func Test_Configure(t *testing.T) {
// 	err := istioAdapter.Configure(app)
// 	if err != nil {
// 		t.Errorf("Unexpected error: %v", err)
// 	}
// }
