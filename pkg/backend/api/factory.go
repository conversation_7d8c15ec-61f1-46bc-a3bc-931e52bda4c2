package api

import (
	"gitlab.leinao.ai/application-controller/apis/application/v1alpha1"
	"gitlab.leinao.ai/application-controller/pkg/backend/assembly"
	"gitlab.leinao.ai/application-controller/pkg/backend/autoscaling"

	//"gitlab.leinao.ai/application-controller/pkg/backend/autoscaling"
	"gitlab.leinao.ai/application-controller/pkg/backend/deploy"
	"gitlab.leinao.ai/application-controller/pkg/backend/empty"
	"gitlab.leinao.ai/application-controller/pkg/backend/gateway"
	rscost "gitlab.leinao.ai/application-controller/pkg/backend/resourcecost"
	"gitlab.leinao.ai/application-controller/pkg/backend/scheduler"
	"gitlab.leinao.ai/application-controller/pkg/backend/service"
	"gitlab.leinao.ai/application-controller/pkg/backend/servicemesh"
	"k8s.io/apimachinery/pkg/runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

var (
	ServiceMeshFactory  = make(map[v1alpha1.ServiceMeshBackend]func() BackendInterface)
	AutoScalingFactory  = make(map[v1alpha1.AutoScalingBackend]func() BackendInterface)
	SchedulerFactory    = make(map[v1alpha1.SchedulerBackend]func() BackendInterface)
	GatewayFactory      = make(map[v1alpha1.GatewayBackend]func() BackendInterface)
	ResourceCostFactory = make(map[string]func() BackendInterface)

	DeployFactory  = make(map[string]func() BackendInterface)
	ServiceFactory = make(map[string]func() BackendInterface)
)

func GetServiceMeshBackend(name v1alpha1.ServiceMeshBackend) BackendInterface {
	if f, found := ServiceMeshFactory[name]; found {
		return f()
	}
	return &empty.Backend{}
}

func GetAutoScalingBackend(name v1alpha1.AutoScalingBackend) BackendInterface {
	if f, found := AutoScalingFactory[name]; found {
		return f()
	}
	return &empty.Backend{}
}

func GetGatewayBackend(name v1alpha1.GatewayBackend) BackendInterface {
	if f, found := GatewayFactory[name]; found {
		return f()
	}
	return &empty.Backend{}
}

func GetSchedulerBackend(name v1alpha1.SchedulerBackend) BackendInterface {
	if f, found := SchedulerFactory[name]; found {
		return f()
	}
	return &empty.Backend{}
}

func GetDeployBackend() BackendInterface {
	if f, found := DeployFactory[v1alpha1.DefaultBackend]; found {
		return f()
	}
	return &empty.Backend{}
}

func GetResourceCostBackend() BackendInterface {
	if f, found := ResourceCostFactory[v1alpha1.DefaultBackend]; found {
		return f()
	}
	return &empty.Backend{}
}

func GetServiceBackend() BackendInterface {
	if f, found := ServiceFactory[v1alpha1.DefaultBackend]; found {
		return f()
	}
	return &empty.Backend{}
}

func SetupAllBackends(client client.Client, scheme *runtime.Scheme, prometheusAddress, hostSuffix string) {
	ServiceFactory[v1alpha1.DefaultBackend] = func() BackendInterface {
		svc := &service.Service{}
		svc.Setup(client, scheme)
		return svc
	}
	ServiceMeshFactory[v1alpha1.Istio] = func() BackendInterface {
		istioMesh := &servicemesh.IstioMesh{Builder: assembly.NewKubernetesBuilder().IstioBuilder(), HostSuffix: hostSuffix}
		istioMesh.Setup(client, scheme)
		return istioMesh
	}
	AutoScalingFactory[v1alpha1.Keda] = func() BackendInterface {
		keda := &autoscaling.KedaAutoScaling{Builder: assembly.NewKubernetesBuilder().AutoscalingBuilder()}
		keda.Setup(client, scheme, prometheusAddress)
		return keda
	}
	// SchedulerFactory[v1alpha1.DefaultBackend] = func() BackendInterface {
	// 	scheduler := &scheduler.DefaultScheduler{}
	// 	scheduler.Setup(client, scheme)
	// 	return scheduler
	// }
	SchedulerFactory[v1alpha1.VolcanoScheduler] = func() BackendInterface {
		scheduler := &scheduler.VolcanoScheduler{}
		scheduler.Setup(client, scheme)
		return scheduler
	}
	GatewayFactory[v1alpha1.IstioGateway] = func() BackendInterface {
		gateway := &gateway.IstioGateway{Builder: assembly.NewKubernetesBuilder().IstioBuilder()}
		gateway.Setup(client, scheme)
		return gateway
	}
	ResourceCostFactory[v1alpha1.DefaultBackend] = func() BackendInterface {
		resourceCost := &rscost.ResourceCost{}
		resourceCost.Setup(client, scheme)
		return resourceCost
	}
	DeployFactory[v1alpha1.DefaultBackend] = func() BackendInterface {
		deploy := &deploy.Deployment{
			Builder: assembly.NewKubernetesBuilder().DeployBuilder(),
		}
		deploy.Setup(client, scheme)
		return deploy
	}
}
