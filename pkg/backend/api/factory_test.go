package api

import (
	"testing"

	systemv1alpha1 "gitlab.leinao.ai/application-controller/apis/application/v1alpha1"
	"k8s.io/apimachinery/pkg/runtime"
	utilruntime "k8s.io/apimachinery/pkg/util/runtime"
	clientgoscheme "k8s.io/client-go/kubernetes/scheme"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"
)

var scheme = runtime.NewScheme()

func init() {
	utilruntime.Must(clientgoscheme.AddToScheme(scheme))
	utilruntime.Must(systemv1alpha1.AddToScheme(scheme))
}

func Test_SetupAllBackends(t *testing.T) {
	c := fake.NewClientBuilder().WithScheme(scheme).Build()
	SetupAllBackends(c, scheme, "127.0.0.1", "127.0.0.1")
	GetServiceBackend()
	GetDeployBackend()
	GetGatewayBackend(systemv1alpha1.IstioGateway)
	GetAutoScalingBackend(systemv1alpha1.Keda)
	GetSchedulerBackend(systemv1alpha1.VolcanoScheduler)
}
