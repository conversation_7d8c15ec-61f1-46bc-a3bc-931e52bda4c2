package deploy

import (
	"reflect"
	"regexp"
	"strings"
	"testing"

	"github.com/nacos-group/nacos-sdk-go/common/logger"
)

// 简化版的函数：用于设置命令和参数
func setContainerCommandAndArgs(command *[]string, args *[]string) {
	if args == nil {
		return
	}
	*command = []string{"sh", "-c"}
	for _, arg := range *args {
		parts := cleanCommandArgumentTest(arg)
		if len(parts) < 2 {
			continue
		}
		// 检查是否是 bash 或 sh 命令
		if isShellCommandTest(parts[0]) {
			processShellCommandTest(parts, command, args, arg)
			return
		}
		// 检查是否包含 bash 或 sh
		if strings.Contains(arg, "bash") {
			*command = []string{"bash", "-c"}
			*args = removeElements(arg)
			return
		}
	}
}

func shellCommand(cmd string) bool {
	return ifhasPrefixRegex(cmd, "/bin/bash") || ifhasPrefixRegex(cmd, "bash") ||
		ifhasPrefixRegex(cmd, "/bin/sh") || ifhasPrefixRegex(cmd, "sh")
}

func ifhasPrefixRegex(s, prefix string) bool {
	// 允许前缀带引号
	re := regexp.MustCompile(`^["']?` + regexp.QuoteMeta(prefix))
	return re.MatchString(s)
}

func removeQuotesTest(arg string) string {
	// 使用正则表达式去除转义的引号
	reUnescape := regexp.MustCompile(`\\["']`)
	return strings.Trim(reUnescape.ReplaceAllString(arg, ""), `"'`)
}

func removeElements(input string) []string {
	// 移除bash/sh/-c，同时尽量保持引号内的内容作为单个元素
	re := regexp.MustCompile(`(["']?\s*(bash|sh)\s*["']?\s*)?((["']?\s*-c\s*["']?\s*)?)`)
	cleanedInput := re.ReplaceAllString(input, "")

	// 检查处理后的命令是否被引号包裹
	match := regexp.MustCompile(`^["']?(.*?)["']?$`).FindStringSubmatch(cleanedInput)
	if len(match) > 1 {
		// 如果整个命令被引号包裹，则将其作为单个元素返回
		return []string{match[1]}
	}

	// 如果没有被引号包裹，将整体作为单个参数返回
	return []string{cleanedInput}
}

func processShellCommandTest(parts []string, command *[]string, args *[]string, arg string) {
	// 使用正则表达式去除转义的引号
	trimmedPart0 := removeQuotesTest(parts[0])
	trimmedPart1 := removeQuotesTest(parts[1])
	if len(parts) > 2 && trimmedPart1 == "-c" {
		trimmedPart2 := removeQuotesTest(parts[2])
		// 确保长度大于等于2，以检查第二个参数
		// 设置为 bash 或 sh 命令
		*command = []string{trimmedPart0}
		*args = []string{"-c", trimmedPart2}
		logger.Infof("set container command and args,command:%+v,arg:%+v", *command, *args)
	} else {
		// 当 -c 缺失或格式不符时保持原样
		*command = []string{"sh", "-c"}
		*args = []string{arg}
	}
	logger.Infof("set container command and args, command: %+v, args: %+v", *command, *args)
}

func cleanCommandArgumentTest(arg string) []string {
	// 如果包含逗号，替换成空格    '"/bin/bash", "-c", "./start_service.sh"'
	arg = strings.ReplaceAll(arg, ",", " ")
	// 使用正则表达式解析参数，确保引号内容被正确处理
	//1、匹配被双引号或单引号包围的字符串
	//1、或者匹配不带引号的非空白字符（即独立的单词）
	re := regexp.MustCompile(`".+?"|'.+?'|\S+`)
	return re.FindAllString(arg, -1)
}

// 判断是否是 sh 或 bash 命令
func isShellCommandTest(cmd string) bool {
	return hasPrefixRegexTest(cmd, "/bin/bash") || hasPrefixRegexTest(cmd, "bash") ||
		hasPrefixRegexTest(cmd, "/bin/sh") || hasPrefixRegexTest(cmd, "sh")
}

func hasPrefixRegexTest(s, prefix string) bool {
	// 允许前缀带引号
	re := regexp.MustCompile(`^["']?` + regexp.QuoteMeta(prefix))
	return re.MatchString(s)
}
func TestSetContainerCommandAndArgs(t *testing.T) {
	tests := []struct {
		name         string
		inputArgs    []string
		expectedCmd  []string
		expectedArgs []string
	}{
		{
			name:         "Bash with script",
			inputArgs:    []string{`"/bin/bash", "-c", "./start_service.sh"`},
			expectedCmd:  []string{"/bin/bash"},
			expectedArgs: []string{"-c", "./start_service.sh"},
		},
		{
			name:         "Sh with script",
			inputArgs:    []string{`bash service.sh`},
			expectedCmd:  []string{"sh", "-c"},
			expectedArgs: []string{"bash service.sh"},
		},
		{
			name:         "Bash with script",
			inputArgs:    []string{`./start_service.sh`},
			expectedCmd:  []string{"sh", "-c"},
			expectedArgs: []string{`./start_service.sh`},
		},
		{
			name:         "Sh with script",
			inputArgs:    []string{`sh service.sh`},
			expectedCmd:  []string{"sh", "-c"},
			expectedArgs: []string{"sh service.sh"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			command := []string{}
			args := tt.inputArgs

			// 调用简化后的逻辑
			setContainerCommandAndArgs(&command, &args)

			// 验证 Command
			if !reflect.DeepEqual(command, tt.expectedCmd) {
				t.Errorf("expected command %v, got %v", tt.expectedCmd, command)
			}

			// 验证 Args
			if !reflect.DeepEqual(args, tt.expectedArgs) {
				t.Errorf("expected args %v, got %v", tt.expectedArgs, args)
			}
		})
	}
}

//func setContainerCommandAndArgs(command *[]string, args *[]string) {
//
//	if args != nil && len(*args) > 0 {
//		// 默认设置 Command 为 "sh -c"
//		*command = []string{"sh", "-c"}
//
//		for _, arg := range *args {
//			// 如果包含逗号，先把逗号替换成空格
//			arg = strings.ReplaceAll(arg, ",", " ")
//			fmt.Printf("Checking arg: %+v\n", arg)
//
//			// 使用正则表达式解析参数，确保引号内容被正确处理
//			re := regexp.MustCompile(`".+?"|'.+?'|\S+`)
//			parts := re.FindAllString(arg, -1)
//			fmt.Printf("Parsed parts: %+v\n", parts)
//
//			// 检查是否是 bash 或 sh 命令
//			//if ifhasPrefixRegex(parts[0], "/bin/bash") || ifhasPrefixRegex(parts[0], "bash") ||
//			//	ifhasPrefixRegex(parts[0], "/bin/sh") || ifhasPrefixRegex(parts[0], "sh") {
//
//			if shellCommand(parts[0]) {
//				if len(parts) > 1 {
//					fmt.Printf("Parsed parts len is : %+v\n,part[1] is %q\n, the equal is %+v", len(parts), parts[1], parts[1] == "-c")
//					// 如果第二个参数是 -c，则继续处理
//					// 去掉转义的双引号和单引号
//
//					// 使用正则表达式去除转义的引号
//					reUnescape := regexp.MustCompile(`\\["']`)
//					trimmedPart1 := reUnescape.ReplaceAllString(parts[1], "")
//					trimmedPart1 = strings.Trim(trimmedPart1, `"'`)
//
//					trimmedPart0 := reUnescape.ReplaceAllString(parts[0], "")
//					//去除多余的引号
//					trimmedPart0 = strings.Trim(trimmedPart0, `"'`)
//
//					trimmedPart2 := reUnescape.ReplaceAllString(parts[2], "")
//					//去除多余的引号
//					trimmedPart2 = strings.Trim(trimmedPart2, `"'`)
//					fmt.Printf("Trimmed part 1: %q\n, part0:%q\n, part2: %q\n", trimmedPart1, trimmedPart0, trimmedPart2)
//					if len(parts) >= 2 && trimmedPart1 == "-c" {
//						// 设置为 bash 或 sh 命令
//						//*command = []string{parts[0]}
//						*command = []string{trimmedPart0}
//						fmt.Printf("the command is ======================%+v\n", *command)
//
//						// 拼接剩余参数
//						//remainingArgs := strings.Join(parts[2:], " ")
//						//*args = []string{"-c", remainingArgs}
//						*args = []string{"-c", trimmedPart2}
//					} else {
//						// 当 -c 缺失或格式不符时保持原样
//						*command = []string{"sh", "-c"}
//						*args = []string{arg}
//					}
//					return
//				}
//			}
//
//			//处理复杂命令，如包含 bash
//			if strings.Contains(arg, "bash") {
//				*command = []string{"bash", "-c"}
//				*args = []string{arg}
//				return
//			}
//		}
//
//	}
//}
