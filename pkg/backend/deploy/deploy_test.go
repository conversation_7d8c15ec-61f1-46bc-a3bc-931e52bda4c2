package deploy

import (
	"fmt"
	"testing"
	"time"

	appv1alpha1 "gitlab.leinao.ai/application-controller/apis/application/v1alpha1"
	"gitlab.leinao.ai/application-controller/pkg/backend/assembly/deploy"
	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	utilruntime "k8s.io/apimachinery/pkg/util/runtime"
	clientgoscheme "k8s.io/client-go/kubernetes/scheme"
	"k8s.io/utils/pointer"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"
)

var app *appv1alpha1.Application
var deployAdapter *Deployment

func init() {
	var scheme = runtime.NewScheme()
	utilruntime.Must(clientgoscheme.AddToScheme(scheme))
	utilruntime.Must(appv1alpha1.AddToScheme(scheme))
	var num int32 = 1
	app = &appv1alpha1.Application{
		ObjectMeta: metav1.ObjectMeta{
			Annotations: map[string]string{
				AnnotationActionKey: ActionStartValue,
			},
			Name:      "llm-predict",
			Namespace: "llm-example",
		},

		Spec: appv1alpha1.ApplicationSpec{
			Version: "v1",
			ServiceMesh: &appv1alpha1.ServiceMeshSpec{
				Backend: appv1alpha1.Istio,
				SubRoute: &appv1alpha1.SubRoute{
					Weight: &num,
					Retries: &appv1alpha1.HTTPRetry{
						AutoRetire: true,
						Attempts:   3,
					},
					TrafficPolicy: &appv1alpha1.TrafficPolicy{
						LoadBalancer: 4,
						CircuitBreaker: &appv1alpha1.CircuitBreaker{
							MaxConnections:          100,
							HTTP1MaxPendingRequests: 100,
							ConsecutiveErrors:       5,
							Interval:                &metav1.Duration{Duration: time.Second * 5},
							BaseEjectionTime:        &metav1.Duration{Duration: time.Minute * 5},
							MaxEjectionPercent:      100,
						},
					},
				},
			},
			AutoScaling: &appv1alpha1.AutoScalingSpec{
				Backend:        appv1alpha1.Keda,
				MinReplicas:    &num,
				MaxReplicas:    &num,
				CooldownPeriod: &num,
				Triggers: []appv1alpha1.ScaleTriggers{
					{
						Type:     "cpu",
						Metadata: map[string]string{"Utilization": "50"},
					},
				},
				Serverless: []appv1alpha1.ScaleTriggers{
					{
						Type:     "prometheus",
						Metadata: map[string]string{"http_requests_total": "20"},
					},
				},
			},
			Scheduler: appv1alpha1.SchedulerSpec{
				SchedulerName: appv1alpha1.KubeScheduler,
			},
			Volumes: []appv1alpha1.VolumeSpec{
				{
					MountPath:       "/data",
					VolumeClaimName: "test-data",
					VolumeClaim: &corev1.PersistentVolumeClaimSpec{
						AccessModes: []corev1.PersistentVolumeAccessMode{corev1.ReadWriteOnce},
						Resources: corev1.ResourceRequirements{
							Requests: corev1.ResourceList{},
						},
					},
				},
			},
			Server: map[string][]appv1alpha1.ServerSpec{
				"image-describe": {
					{
						Version:  "v1",
						Replicas: pointer.Int32Ptr(1),
						Template: corev1.PodTemplateSpec{
							ObjectMeta: metav1.ObjectMeta{
								Labels: map[string]string{"app": "image-describe"},
							},
							Spec: corev1.PodSpec{
								Containers: []corev1.Container{
									{
										Name:            "image-describe",
										Image:           "registry.cn-hangzhou.aliyuncs.com/ruanxingbaozi/pytorch:image_recognition_demo",
										ImagePullPolicy: corev1.PullAlways,
										Args:            []string{"bash", "-c", "python image_recognition_server.py"},
										Ports: []corev1.ContainerPort{
											{
												Name:          "http",
												ContainerPort: 5000,
											},
										},
									},
								},
							},
						},
						ServiceMesh: &appv1alpha1.ServiceMeshSpec{
							Backend: appv1alpha1.Istio,
							SubRoute: &appv1alpha1.SubRoute{
								Weight: &num,
								Retries: &appv1alpha1.HTTPRetry{
									AutoRetire: true,
									Attempts:   3,
								},
								TrafficPolicy: &appv1alpha1.TrafficPolicy{
									LoadBalancer: 4,
									CircuitBreaker: &appv1alpha1.CircuitBreaker{
										MaxConnections:          100,
										HTTP1MaxPendingRequests: 100,
										ConsecutiveErrors:       5,
										Interval:                &metav1.Duration{Duration: time.Second * 5},
										BaseEjectionTime:        &metav1.Duration{Duration: time.Minute * 5},
										MaxEjectionPercent:      100,
									},
								},
							},
						},
						AutoScaling: &appv1alpha1.AutoScalingSpec{
							Backend:        appv1alpha1.Keda,
							MinReplicas:    &num,
							MaxReplicas:    &num,
							CooldownPeriod: &num,
							Triggers: []appv1alpha1.ScaleTriggers{
								{
									Type:     "cpu",
									Metadata: map[string]string{"Utilization": "50"},
								},
							},
							Serverless: []appv1alpha1.ScaleTriggers{
								{
									Type:     "prometheus",
									Metadata: map[string]string{"http_requests_total": "20"},
								},
							},
						},
					},
				},
			},
		},
		Status: appv1alpha1.ApplicationStatus{
			Phase: appv1alpha1.Running,
		},
	}
	dp := &appsv1.Deployment{
		ObjectMeta: metav1.ObjectMeta{
			Name:      fmt.Sprintf("%s-%s", "image-describe", "v1"),
			Namespace: app.GetNamespace(),
		},
		Spec: appsv1.DeploymentSpec{
			Replicas: &num,
		},
	}

	objs := []client.Object{
		app, dp,
	}
	deployAdapter = &Deployment{
		Builder: deploy.NewDeployBuilder(),
	}

	deployAdapter.Setup(
		fake.NewClientBuilder().WithScheme(scheme).WithObjects(objs...).Build(),
		scheme,
	)
}

func Test_Configure(t *testing.T) {
	err := deployAdapter.Configure(app)
	if err != nil {
		t.Errorf("Unexpected error: %v", err)
	}
}

func Test_Name(t *testing.T) {
	name := deployAdapter.Name()
	if name != "deployment" {
		t.Errorf("Unexpected result: %v", name)
	}
}

func Test_Clean(t *testing.T) {
	err := deployAdapter.Clean(app)
	if err != nil {
		t.Errorf("Unexpected error: %v", err)
	}
}

func Test_Inject(t *testing.T) {
	err := deployAdapter.Inject(app)
	if err != nil {
		t.Errorf("Unexpected error: %v", err)
	}
}
