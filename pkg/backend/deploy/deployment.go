package deploy

import (
	"context"
	"fmt"
	"regexp"
	"strings"

	"github.com/nacos-group/nacos-sdk-go/common/logger"
	"gitlab.leinao.ai/application-controller/apis/application/v1alpha1"
	"gitlab.leinao.ai/application-controller/pkg/backend/assembly/deploy"
	v1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/klog"
	"k8s.io/utils/ptr"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"
)

const (
	AnnotationActionKey  = "application.leinao.ai/action"
	ActionUpdateValue    = "update"
	ActionStartValue     = "start"
	CacheVolumeName      = "cache-volume"
	CacheVolumeSize      = "2Gi"
	CacheVolumeMountPath = "/dev/shm"
	ImageSecret          = "image-secret"
)

// 定义常量
const (
	AscendDriverVolume   = "ascend-driver-volume"  // 昇腾驱动卷名称
	AscendAddOnsVolume   = "ascend-add-ons-volume" // 昇腾插件卷名称
	AscendDriverPath     = "/usr/local/Ascend/driver"
	AscendAddOnsPath     = "/usr/local/Ascend/add-ons"
	HuaweiResourcePrefix = "huawei.com/"
)

type Deployment struct {
	client client.Client
	*deploy.Builder
	*runtime.Scheme
}

func (d *Deployment) Configure(app *v1alpha1.Application) error {
	namespace := app.Namespace
	for serverName, servers := range app.Spec.Server {
		for i := range servers {
			deployment := &v1.Deployment{
				ObjectMeta: metav1.ObjectMeta{
					Name:      fmt.Sprintf("%s-%s", serverName, servers[i].Version),
					Namespace: namespace,
				},
			}

			//适配keda扩缩，如果开启了弹性伸缩，对于deployment副本，如果只改了副本，并对比副本数在hpa的最大最小值范围内，则不更新，直接return
			// 指定要获取的 Deployment 的名称和命名空间
			// if d.shouldSkipCreateOrUpdate(servers[i], app, serverName, namespace) {
			// 	return nil
			// }

			if _, err := controllerutil.CreateOrUpdate(context.TODO(), d.client, deployment, d.MutateFn(deployment, serverName, app, &servers[i])); err != nil {
				return err
			}

		}

	}

	return nil
}

// func (d *Deployment) shouldSkipCreateOrUpdate(serverSpecs v1alpha1.ServerSpec, app *v1alpha1.Application, serverName, namespace string) bool {
// 	//判断是否开启了弹性伸缩或者serverless缩为0，因为已经在keda.go处理了merge,所以可以只判断serverSpec

// 	if shouldProcessAutoScaling(app, serverSpecs) {
// 		deploymentKey := client.ObjectKey{
// 			Name:      fmt.Sprintf("%s-%s", serverName, serverSpecs.Version),
// 			Namespace: namespace,
// 		}

// 		// 创建一个 Deployment 对象，用于存储获取的信息
// 		var deploymentObj v1.Deployment

// 		// 使用 Get 方法获取 Deployment 的信息
// 		err := d.client.Get(context.TODO(), deploymentKey, &deploymentObj)
// 		if err != nil {
// 			//如果没查到deployment，则不跳过,因为表示是第一次创建
// 			if client.IgnoreNotFound(err) == nil {
// 				return false
// 			}
// 			klog.Errorf("Get deployment key %s: %v", deploymentKey, err)
// 		}

// 		// 获取 Deployment 的副本数
// 		deploymentReplicas := *deploymentObj.Spec.Replicas
// 		var serverSpecsReplica int32 = 1
// 		if serverSpecs.Replicas != nil {
// 			serverSpecsReplica = *serverSpecs.Replicas
// 		}

// 		//需要增加判断启动及更新服务的情况下在没更新完整前不需要跳过创建deployment的创建或者更新操作，启动或者更新完整后则跳过deploy的创建或者更新
// 		if actionStartsOrUpdatesIncomplete(app) {
// 			return false
// 		}

// 		// 检查开启弹性伸缩及serverless情况下是否需要跳过deployment的创建或更新操作

// 		if serverSpecs.AutoScaling.Triggers != nil &&
// 			serverSpecsReplica != deploymentReplicas &&
// 			*serverSpecs.AutoScaling.MinReplicas < deploymentReplicas &&
// 			deploymentReplicas < *serverSpecs.AutoScaling.MaxReplicas {
// 			return true
// 		}

// 		if serverSpecs.AutoScaling.Serverless != nil &&
// 			serverSpecsReplica != deploymentReplicas {
// 			return true
// 		}

// 	}
// 	return false
// }

// func shouldProcessAutoScaling(app *v1alpha1.Application, serverSpecs v1alpha1.ServerSpec) bool {
// 	return app.Spec.AutoScaling != nil && app.Spec.AutoScaling.Backend == v1alpha1.Keda && serverSpecs.AutoScaling != nil &&
// 		(serverSpecs.AutoScaling.Triggers != nil || serverSpecs.AutoScaling.Serverless != nil)
// }

func actionStartsOrUpdatesIncomplete(app *v1alpha1.Application) bool {
	action, ok := app.GetAnnotations()[AnnotationActionKey]
	return ok && (action == ActionStartValue || action == ActionUpdateValue) && app.Status.Phase != v1alpha1.Running
}

func (d *Deployment) Name() string {
	return "deployment"
}

func (d *Deployment) Inject(app *v1alpha1.Application) error {
	err := d.injectPodTemplate(app)
	if err != nil {
		return err
	}
	return nil
}

func (d *Deployment) Setup(client client.Client, scheme *runtime.Scheme) {
	d.client = client
	d.Scheme = scheme
}

func (d *Deployment) MutateFn(deployment *v1.Deployment, serverName string, app *v1alpha1.Application, serverSpec *v1alpha1.ServerSpec) controllerutil.MutateFn {
	return func() error {
		d.BuildDeployment(deployment, serverName, app, serverSpec)
		deployment.SetOwnerReferences(nil)
		return controllerutil.SetControllerReference(app, deployment, d.Scheme)

	}
}

func (d *Deployment) Clean(app *v1alpha1.Application) error {
	namespace := app.Namespace
	for serverName, servers := range app.Spec.Server {
		for _, serverSpecs := range servers {
			deployment := &v1.Deployment{
				ObjectMeta: metav1.ObjectMeta{
					Name:      fmt.Sprintf("%s-%s", serverName, serverSpecs.Version),
					Namespace: namespace,
				},
			}
			if err := d.client.Get(context.TODO(), client.ObjectKeyFromObject(deployment), deployment); err != nil {
				return err
			}

			if _, err := controllerutil.CreateOrUpdate(context.TODO(), d.client, deployment, func() error {
				deployment.Spec.Replicas = ptr.To[int32](0)
				return nil
			}); err != nil {
				return err
			}
		}

	}

	return nil

}

func IsNoGpuResource(serverSpec *v1alpha1.ServerSpec) bool {
	isNoGpuRes := true
	for rName := range serverSpec.Template.Spec.Containers[0].Resources.Requests {
		switch rName {
		case corev1.ResourceCPU, corev1.ResourceMemory:
			continue
		default:
			isNoGpuRes = false
		}
	}
	return isNoGpuRes
}

func (d *Deployment) ensureServiceAccountExists(app *v1alpha1.Application) error {
	serviceAccountName := app.Name
	namespace := app.Namespace
	key := client.ObjectKey{Name: serviceAccountName, Namespace: namespace}
	serviceAccount := &corev1.ServiceAccount{
		ObjectMeta: metav1.ObjectMeta{
			Name:      serviceAccountName,
			Namespace: app.Namespace,
		},
	}

	if err := controllerutil.SetControllerReference(app, serviceAccount, d.Scheme); err != nil {
		return err
	}

	if err := d.client.Get(context.TODO(), key, serviceAccount); err != nil {
		if client.IgnoreNotFound(err) != nil {
			return err
		}
		if _, err := controllerutil.CreateOrUpdate(context.TODO(), d.client, serviceAccount, func() error { return nil }); err != nil {
			return err
		}
	}
	return nil
}
func (d *Deployment) injectPodTemplate(app *v1alpha1.Application) error {
	if err := d.ensureServiceAccountExists(app); err != nil {
		return err
	}

	// 考虑多服务，查找 X-User-Agent-Key
	xUserAgentKeyEnv := ""
	for _, servers := range app.Spec.Server {
		for i := range servers {
			// 获取X-User-Agent-Key
			if servers[i].ServiceMesh == nil || servers[i].ServiceMesh.SubRoute == nil {
				continue
			}
			if servers[i].ServiceMesh.SubRoute.Match != nil {
				for j := range servers[i].ServiceMesh.SubRoute.Match {
					if value, ok := servers[i].ServiceMesh.SubRoute.Match[j].Headers["X-User-Agent-Key"]; ok {
						xUserAgentKeyEnv = value.Exact
						goto configTemplate
					}
				}
			}
		}
	}

	klog.Infof("xUserAgentKeyEnv:%v", xUserAgentKeyEnv)
configTemplate:
	for serverName, servers := range app.Spec.Server {
		for i := range servers {
			d.injectServerTemplate(app, serverName, &servers[i], xUserAgentKeyEnv)
		}
	}
	return nil
}

func (d *Deployment) injectServerTemplate(app *v1alpha1.Application, serverName string, serverSpec *v1alpha1.ServerSpec, xUserAgentKeyEnv string) {
	podTemplate := serverSpec.Template
	podTemplate.Spec.ServiceAccountName = app.Name

	podTemplate.ObjectMeta.Labels = d.getLabels(app, serverName, serverSpec)
	podTemplate.ObjectMeta.Annotations = d.getAnnotations(serverSpec)
	podTemplate.Spec.Containers[0].Env = d.getPodTemplateEnv(serverSpec, xUserAgentKeyEnv)
	podTemplate.Spec.ImagePullSecrets = []corev1.LocalObjectReference{
		{
			Name: ImageSecret,
		},
	}

	//args不为空的时候才给command赋初值
	if podTemplate.Spec.Containers[0].Args != nil {
		podTemplate.Spec.Containers[0].Command = []string{"sh", "-c"}
	}

	//如果用户传的命令里包含bash （其他shell 解释器暂不考虑），则覆盖sh -c
	for i := range podTemplate.Spec.Containers {
		d.setContainerCommandAndArgs(&podTemplate.Spec.Containers[i])
	}

	////共享内存 先写死2Gi
	//sizeLimit := resource.MustParse(CacheVolumeSize)
	//
	//cacheVolume := corev1.Volume{
	//	Name: CacheVolumeName, // 卷的名称
	//	VolumeSource: corev1.VolumeSource{
	//		EmptyDir: &corev1.EmptyDirVolumeSource{
	//			Medium:    corev1.StorageMediumMemory, // 配置卷为使用共享内存
	//			SizeLimit: &sizeLimit,
	//		},
	//	},
	//}
	//podTemplate.Spec.Volumes = append(podTemplate.Spec.Volumes, cacheVolume)
	//cacheVolumeMount := corev1.VolumeMount{
	//	Name:      CacheVolumeName,
	//	MountPath: CacheVolumeMountPath,
	//}
	//podTemplate.Spec.Containers[0].VolumeMounts = append(podTemplate.Spec.Containers[0].VolumeMounts, cacheVolumeMount)

	if isHuaweiResourceRequested(podTemplate.Spec.Containers[0].Resources.Requests) {

		// 添加昇腾驱动卷
		ascendDriverVolume := corev1.Volume{
			Name: AscendDriverVolume,
			VolumeSource: corev1.VolumeSource{
				HostPath: &corev1.HostPathVolumeSource{
					Path: AscendDriverPath,
					Type: func() *corev1.HostPathType {
						t := corev1.HostPathDirectory
						return &t
					}(),
				},
			},
		}
		podTemplate.Spec.Volumes = append(podTemplate.Spec.Volumes, ascendDriverVolume)

		// 添加昇腾插件卷
		ascendAddOnsVolume := corev1.Volume{
			Name: AscendAddOnsVolume,
			VolumeSource: corev1.VolumeSource{
				HostPath: &corev1.HostPathVolumeSource{
					Path: AscendAddOnsPath,
					Type: func() *corev1.HostPathType {
						t := corev1.HostPathDirectory
						return &t
					}(),
				},
			},
		}
		podTemplate.Spec.Volumes = append(podTemplate.Spec.Volumes, ascendAddOnsVolume)

		// 添加昇腾驱动挂载
		ascendDriverVolumeMount := corev1.VolumeMount{
			Name:      AscendDriverVolume,
			MountPath: AscendDriverPath,
		}
		podTemplate.Spec.Containers[0].VolumeMounts = append(podTemplate.Spec.Containers[0].VolumeMounts, ascendDriverVolumeMount)

		// 添加昇腾插件挂载
		ascendAddOnsVolumeMount := corev1.VolumeMount{
			Name:      AscendAddOnsVolume,
			MountPath: AscendAddOnsPath,
		}
		podTemplate.Spec.Containers[0].VolumeMounts = append(podTemplate.Spec.Containers[0].VolumeMounts, ascendAddOnsVolumeMount)
		// for _, volSpec := range app.Spec.Volumes {
		// 	d.injectVolume(&volSpec, &podTemplate)
		// }
	}

	serverSpec.Template = podTemplate
}

// 判断是否申请了以 huawei.com 开头的资源
func isHuaweiResourceRequested(resources corev1.ResourceList) bool {
	for resourceName := range resources {
		if strings.HasPrefix(string(resourceName), HuaweiResourcePrefix) {
			return true
		}
	}
	return false
}

func (d *Deployment) getLabels(app *v1alpha1.Application, serverName string, serverSpec *v1alpha1.ServerSpec) map[string]string {
	existingLabels := serverSpec.Template.ObjectMeta.Labels
	if existingLabels == nil {
		existingLabels = make(map[string]string)
	}
	existingLabels[v1alpha1.LabelAppName] = serverName
	existingLabels[v1alpha1.LabelAppversion] = serverSpec.Version
	existingLabels[v1alpha1.LabelApplicationName] = app.Name
	existingLabels[v1alpha1.LabelApplicationVersion] = app.Spec.Version

	return existingLabels
}

// get annotations
func (d *Deployment) getAnnotations(serverSpec *v1alpha1.ServerSpec) map[string]string {
	existingAnnotations := serverSpec.Template.ObjectMeta.Annotations
	if existingAnnotations == nil {
		existingAnnotations = make(map[string]string)
	}
	existingAnnotations["leinao.ai/storage-managed"] = "true"
	return existingAnnotations
}

// get podtemplate  env
func (d *Deployment) getPodTemplateEnv(serverSpec *v1alpha1.ServerSpec, xUserAgentKeyEnv string) []corev1.EnvVar {
	existingEnv := serverSpec.Template.Spec.Containers[0].Env
	if existingEnv == nil {
		existingEnv = make([]corev1.EnvVar, 0)
	}
	// 设置 NVIDIA_VISIBLE_DEVICES="" 来避免没有申请 GPU 资源的容器内看得见 GPU
	if IsNoGpuResource(serverSpec) {
		existingEnv = append(existingEnv, corev1.EnvVar{Name: "NVIDIA_VISIBLE_DEVICES", Value: ""})
	}

	// 使用 Downward API 注入 Pod IP
	podIPEnvVar := corev1.EnvVar{
		Name: "POD_IP",
		ValueFrom: &corev1.EnvVarSource{
			FieldRef: &corev1.ObjectFieldSelector{
				FieldPath: "status.podIP",
			},
		},
	}
	existingEnv = append(existingEnv, podIPEnvVar)
	if len(xUserAgentKeyEnv) > 0 {
		// 注入X-User-Agent-Key
		xUserAgentKeyEnv := corev1.EnvVar{
			Name:  "X-User-Agent-Key",
			Value: xUserAgentKeyEnv,
		}
		existingEnv = append(existingEnv, xUserAgentKeyEnv)
	}

	return existingEnv
}

func (d *Deployment) injectVolume(volSpec *v1alpha1.VolumeSpec, podTemplate *corev1.PodTemplateSpec) {
	volName := volSpec.VolumeClaimName
	if !volumeExists(volName, podTemplate.Spec.Volumes) {
		volume := corev1.Volume{
			Name: volName,
			VolumeSource: corev1.VolumeSource{
				PersistentVolumeClaim: &corev1.PersistentVolumeClaimVolumeSource{
					ClaimName: volName,
				},
			},
		}
		podTemplate.Spec.Volumes = append(podTemplate.Spec.Volumes, volume)
	}

	if !volumeMountExists(volName, podTemplate.Spec.Containers[0].VolumeMounts) {
		volumeMount := corev1.VolumeMount{
			Name:      volName,
			MountPath: volSpec.MountPath,
		}

		podTemplate.Spec.Containers[0].VolumeMounts = append(podTemplate.Spec.Containers[0].VolumeMounts, volumeMount)
	}
}

func volumeExists(volName string, volumes []corev1.Volume) bool {
	for _, v := range volumes {
		if v.Name == volName {
			return true
		}
	}
	return false
}

func volumeMountExists(volName string, volumeMounts []corev1.VolumeMount) bool {
	for _, v := range volumeMounts {
		if v.Name == volName {
			return true
		}
	}
	return false
}

func (d *Deployment) setContainerCommandAndArgs(container *corev1.Container) {
	if container.Args == nil {
		return
	}
	container.Command = []string{"sh", "-c"}
	for _, arg := range container.Args {
		parts := cleanCommandArgument(arg)
		if len(parts) < 2 {
			continue
		}
		// 检查是否是 bash 或 sh 命令
		if isShellCommand(parts[0]) {
			processShellCommand(parts, container, arg)
			return
		}
		// 检查是否包含 bash 或 sh
		if strings.Contains(arg, "bash") {
			container.Command = []string{"bash", "-c"}
			container.Args = d.removeElements(arg)
			return
		}
	}
}

func removeQuotes(arg string) string {
	// 使用正则表达式去除转义的引号
	reUnescape := regexp.MustCompile(`\\["']`)
	return strings.Trim(reUnescape.ReplaceAllString(arg, ""), `"'`)
}

func processShellCommand(parts []string, container *corev1.Container, arg string) {
	// 使用正则表达式去除转义的引号
	trimmedPart0 := removeQuotes(parts[0])
	trimmedPart1 := removeQuotes(parts[1])
	if len(parts) > 2 && trimmedPart1 == "-c" {
		trimmedPart2 := removeQuotes(parts[2])
		// 确保长度大于等于2，以检查第二个参数
		// 设置为 bash 或 sh 命令
		container.Command = []string{trimmedPart0}
		container.Args = []string{"-c", trimmedPart2}
		logger.Infof("set container command and args,command:%+v,arg:%+v", container.Command, container.Args)
	} else {
		// 当 -c 缺失或格式不符时保持原样
		container.Command = []string{"sh", "-c"}
		container.Args = []string{arg}
	}
	logger.Infof("set container command and args, command: %+v, args: %+v", container.Command, container.Args)
}

func cleanCommandArgument(arg string) []string {
	// 如果包含逗号，替换成空格    '"/bin/bash", "-c", "./start_service.sh"'
	arg = strings.ReplaceAll(arg, ",", " ")
	// 使用正则表达式解析参数，确保引号内容被正确处理
	//1、匹配被双引号或单引号包围的字符串
	//1、或者匹配不带引号的非空白字符（即独立的单词）
	re := regexp.MustCompile(`".+?"|'.+?'|\S+`)
	return re.FindAllString(arg, -1)
}

// 判断是否是 sh 或 bash 命令
func isShellCommand(cmd string) bool {
	return hasPrefixRegex(cmd, "/bin/bash") || hasPrefixRegex(cmd, "bash") ||
		hasPrefixRegex(cmd, "/bin/sh") || hasPrefixRegex(cmd, "sh")
}

func hasPrefixRegex(s, prefix string) bool {
	// 允许前缀带引号
	re := regexp.MustCompile(`^["']?` + regexp.QuoteMeta(prefix))
	return re.MatchString(s)
}

// removeElements 优化处理用户输入,页面上可能的arg输入,全部兼容处理
// input := `"bash" "-c" python llm_server.py`
// input2 := `"bash" "python llm_server.py"`
// input3 := `"sh" "-c" python llm_server.py`
// input4 := `"sh" "python llm_server.py"`
// input5 := `bash python llm_server.py`
// input6 := `"sh" python llm_server.py`
// input7 := `sh python llm_server.py`
// input8 := `sh -c python llm_server.py`
// input9 := `bash service.sh`
// input10 := `sh service.sh`
// input11 := `"/bin/bash", "-c", "./start_service.sh"`
// input12 := `./start_service.sh`
func (d *Deployment) removeElements(input string) []string {
	// 移除bash/sh/-c，同时尽量保持引号内的内容作为单个元素
	re := regexp.MustCompile(`(["']?\s*(bash|sh)\s*["']?\s*)?((["']?\s*-c\s*["']?\s*)?)`)
	cleanedInput := re.ReplaceAllString(input, "")

	// 检查处理后的命令是否被引号包裹
	match := regexp.MustCompile(`^["']?(.*?)["']?$`).FindStringSubmatch(cleanedInput)
	if len(match) > 1 {
		// 如果整个命令被引号包裹，则将其作为单个元素返回
		return []string{match[1]}
	}

	// 如果没有被引号包裹，将整体作为单个参数返回
	return []string{cleanedInput}
}
