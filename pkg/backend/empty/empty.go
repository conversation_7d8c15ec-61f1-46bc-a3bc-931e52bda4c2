package empty

import (
	"gitlab.leinao.ai/application-controller/apis/application/v1alpha1"
	"k8s.io/apimachinery/pkg/runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

type Backend struct {
}

func (e *Backend) Name() string {
	return "empty"
}

func (e *Backend) Configure(app *v1alpha1.Application) error {
	return nil
}

func (e *Backend) Inject(app *v1alpha1.Application) error {
	return nil
}

func (e *Backend) Setup(client client.Client, scheme *runtime.Scheme) {
}

func (e *Backend) Clean(app *v1alpha1.Application) error {
	return nil
}
