package servicemesh

import (
	"testing"
	"time"

	appv1alpha1 "gitlab.leinao.ai/application-controller/apis/application/v1alpha1"
	"gitlab.leinao.ai/application-controller/pkg/backend/assembly/istio"
	clientnetworking "istio.io/client-go/pkg/apis/networking/v1alpha3"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	utilruntime "k8s.io/apimachinery/pkg/util/runtime"
	clientgoscheme "k8s.io/client-go/kubernetes/scheme"
	"k8s.io/utils/pointer"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"
)

var app *appv1alpha1.Application
var istioMesh *IstioMesh

func init() {
	var scheme = runtime.NewScheme()
	utilruntime.Must(clientgoscheme.AddToScheme(scheme))
	utilruntime.Must(appv1alpha1.AddToScheme(scheme))
	utilruntime.Must(clientnetworking.AddToScheme(scheme))

	var num int32 = 1
	app = &appv1alpha1.Application{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "llm-predict",
			Namespace: "llm-example",
		},

		Spec: appv1alpha1.ApplicationSpec{
			Version: "v1",
			ServiceMesh: &appv1alpha1.ServiceMeshSpec{
				Backend: appv1alpha1.Istio,
				SubRoute: &appv1alpha1.SubRoute{
					Weight: &num,
					Retries: &appv1alpha1.HTTPRetry{
						AutoRetire: true,
						Attempts:   3,
					},
					TrafficPolicy: &appv1alpha1.TrafficPolicy{
						LoadBalancer: 4,
						CircuitBreaker: &appv1alpha1.CircuitBreaker{
							MaxConnections:          100,
							HTTP1MaxPendingRequests: 100,
							ConsecutiveErrors:       5,
							Interval:                &metav1.Duration{Duration: time.Second * 5},
							BaseEjectionTime:        &metav1.Duration{Duration: time.Minute * 5},
							MaxEjectionPercent:      100,
						},
					},
				},
			},
			Server: map[string][]appv1alpha1.ServerSpec{
				"image-describe": {
					{
						Version:  "v1",
						Replicas: pointer.Int32Ptr(1),
						Template: corev1.PodTemplateSpec{
							ObjectMeta: metav1.ObjectMeta{
								Labels: map[string]string{"app": "image-describe"},
							},
							Spec: corev1.PodSpec{
								Containers: []corev1.Container{
									{
										Name:            "image-describe",
										Image:           "registry.cn-hangzhou.aliyuncs.com/ruanxingbaozi/pytorch:image_recognition_demo",
										ImagePullPolicy: corev1.PullAlways,
										Args:            []string{"bash", "-c", "python image_recognition_server.py"},
										Ports: []corev1.ContainerPort{
											{
												Name:          "http",
												ContainerPort: 5000,
											},
										},
									},
								},
							},
						},
						ServiceMesh: &appv1alpha1.ServiceMeshSpec{
							Backend: appv1alpha1.Istio,
							SubRoute: &appv1alpha1.SubRoute{
								Weight: &num,
								Retries: &appv1alpha1.HTTPRetry{
									AutoRetire: true,
									Attempts:   3,
								},
								TrafficPolicy: &appv1alpha1.TrafficPolicy{
									LoadBalancer: 4,
									CircuitBreaker: &appv1alpha1.CircuitBreaker{
										MaxConnections:          100,
										HTTP1MaxPendingRequests: 100,
										ConsecutiveErrors:       5,
										Interval:                &metav1.Duration{Duration: time.Second * 5},
										BaseEjectionTime:        &metav1.Duration{Duration: time.Minute * 5},
										MaxEjectionPercent:      100,
									},
								},
							},
						},
					},
				},
			},
		},
		Status: appv1alpha1.ApplicationStatus{
			Phase: appv1alpha1.Running,
		},
	}

	vs := &clientnetworking.VirtualService{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "image-describe",
			Namespace: app.Namespace,
		},
	}

	dr := &clientnetworking.DestinationRule{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "image-describe",
			Namespace: app.Namespace,
		},
	}

	objs := []client.Object{
		app, vs, dr,
	}
	istioMesh = &IstioMesh{
		Builder: istio.NewIstioBuilder(),
	}
	istioMesh.Setup(
		fake.NewClientBuilder().WithScheme(scheme).WithObjects(objs...).Build(),
		scheme,
	)
}

func Test_Inject(t *testing.T) {
	var num int32 = 1
	newApp := app.DeepCopy()
	for name, server := range newApp.Spec.Server {
		for index, _ := range server {
			newApp.Spec.Server[name][index].ServiceMesh.SubRoute.Weight = &num
			newApp.Spec.Server[name][index].ServiceMesh.SubRoute.Match = nil
			newApp.Spec.Server[name][index].ServiceMesh.SubRoute.Retries = nil
			newApp.Spec.Server[name][index].ServiceMesh.SubRoute.TrafficPolicy = nil
		}
	}
	err := istioMesh.Inject(newApp)
	if err != nil {
		t.Errorf("Unexpected error: %v", err)
	}
}

func Test_Configure(t *testing.T) {
	err := istioMesh.Configure(app)
	if err != nil {
		t.Errorf("Unexpected error: %v", err)
	}
}
