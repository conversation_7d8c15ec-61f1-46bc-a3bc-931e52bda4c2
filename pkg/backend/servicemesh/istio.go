package servicemesh

import (
	"context"
	"sync"

	"gitlab.leinao.ai/application-controller/apis/application/v1alpha1"
	"gitlab.leinao.ai/application-controller/pkg/backend/assembly/istio"
	"gitlab.leinao.ai/application-controller/pkg/utils/controller"
	clientnetworking "istio.io/client-go/pkg/apis/networking/v1alpha3"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"
)

type IstioMesh struct {
	sync.Once
	client.Client
	*istio.Builder
	*runtime.Scheme
	HostSuffix string
}

func (i *IstioMesh) Configure(app *v1alpha1.Application) error {
	for name, server := range app.Spec.Server {
		if server[0].ServiceMesh == nil {
			continue
		}
		vs := &clientnetworking.VirtualService{
			ObjectMeta: metav1.ObjectMeta{
				Name:      name,
				Namespace: app.Namespace,
			},
		}

		if _, err := controller.CreateOrUpdate(context.Background(), i.Client, vs, i.MutateVirtualServiceFn(vs, app, name, i.HostSuffix)); err != nil {
			return err
		}
		dr := &clientnetworking.DestinationRule{
			ObjectMeta: metav1.ObjectMeta{
				Name:      name,
				Namespace: app.Namespace,
			},
		}
		if _, err := controller.CreateOrUpdate(context.Background(), i.Client, dr, i.MutateDestinationRuleFn(dr, app, name)); err != nil {
			return err
		}
	}
	return nil
}

func (i *IstioMesh) Inject(app *v1alpha1.Application) error {

	// add lable for istio
	for _, servers := range app.Spec.Server {
		for _, server := range servers {
			server.Template.Labels["sidecar.istio.io/inject"] = "true"
		}
	}
	globalMesh := app.Spec.ServiceMesh.DeepCopy()
	for name, server := range app.Spec.Server {
		for index, ser := range server {
			if sm := merge(globalMesh, ser.ServiceMesh); sm != nil {
				app.Spec.Server[name][index].ServiceMesh = sm
			}
		}
	}

	return nil
}

func (i *IstioMesh) Setup(client client.Client, scheme *runtime.Scheme) {
	i.Once.Do(func() {
		i.Client = client
		i.Scheme = scheme
	})
}

func (i *IstioMesh) Name() string {
	return "istio-1.19"
}
func (i *IstioMesh) Clean(app *v1alpha1.Application) error {
	return nil
}

func merge(globalMesh, local *v1alpha1.ServiceMeshSpec) *v1alpha1.ServiceMeshSpec {
	if globalMesh == nil {
		return local
	}
	if local == nil {
		globalMesh.IngressGateway = false
		if globalMesh.SubRoute != nil && globalMesh.SubRoute.Match != nil {
			globalMesh.SubRoute.Match = nil
		}
		local = globalMesh
		return local
	}

	if globalMesh.SubRoute == nil {
		return local
	}

	if local.SubRoute == nil {
		local.SubRoute = globalMesh.SubRoute
		return local
	}

	if local.SubRoute.Retries == nil {
		local.SubRoute.Retries = globalMesh.SubRoute.Retries
	}

	if local.SubRoute.Match == nil {
		local.SubRoute.Match = globalMesh.SubRoute.Match
	}

	if local.SubRoute.TrafficPolicy == nil {
		local.SubRoute.TrafficPolicy = globalMesh.SubRoute.TrafficPolicy
	}

	if local.SubRoute.TrafficPolicy != nil && local.SubRoute.TrafficPolicy.CircuitBreaker == nil && globalMesh.SubRoute.TrafficPolicy != nil {
		local.SubRoute.TrafficPolicy.CircuitBreaker = globalMesh.SubRoute.TrafficPolicy.CircuitBreaker
	}
	return local
}

func (i *IstioMesh) MutateVirtualServiceFn(vs *clientnetworking.VirtualService, app *v1alpha1.Application, name, hostSuffix string) controllerutil.MutateFn {
	return func() error {
		i.BuildVirtualService(vs, name, hostSuffix, app)
		vs.SetOwnerReferences(nil)
		return controllerutil.SetControllerReference(app, vs, i.Scheme)
	}
}

func (i *IstioMesh) MutateDestinationRuleFn(dr *clientnetworking.DestinationRule, app *v1alpha1.Application, name string) controllerutil.MutateFn {
	return func() error {
		i.BuildDestinationRule(dr, name, app.Spec.Server[name])
		dr.SetOwnerReferences(nil)
		return controllerutil.SetControllerReference(app, dr, i.Scheme)
	}
}
