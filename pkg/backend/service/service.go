package service

import (
	"context"

	"k8s.io/apimachinery/pkg/runtime"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/intstr"
	"sigs.k8s.io/controller-runtime/pkg/client"

	"gitlab.leinao.ai/application-controller/apis/application/v1alpha1"
)

const (
	ProtocolHTTP = "http"
)

type Service struct {
	client.Client
	*runtime.Scheme
}

func (v *Service) Configure(app *v1alpha1.Application) error {
	for name, servers := range app.Spec.Server {
		server := servers[0]
		svc := &corev1.Service{
			TypeMeta: metav1.TypeMeta{},
			ObjectMeta: metav1.ObjectMeta{
				Labels: map[string]string{
					"app": name,
				},
				Name:      name,
				Namespace: app.Namespace,
			},
		}
		if err := controllerutil.SetControllerReference(app, svc, v.Scheme); err != nil {
			return err
		}
		_, err := controllerutil.CreateOrUpdate(context.Background(), v.Client, svc, func() error {
			// update the svc
			var containerPort int32
			if len(server.Template.Spec.Containers[0].Ports) == 0 {
				containerPort = 80
			} else {
				containerPort = server.Template.Spec.Containers[0].Ports[0].ContainerPort
			}
			svc.Spec = corev1.ServiceSpec{
				Selector: map[string]string{
					v1alpha1.LabelAppName: name,
				},
				Ports: []corev1.ServicePort{
					{
						Name:     ProtocolHTTP,
						Port:     containerPort,
						Protocol: corev1.ProtocolTCP,
						TargetPort: intstr.IntOrString{
							Type:   intstr.Int,
							IntVal: containerPort,
						},
					},
				},
			}
			return nil
		})
		if err != nil {
			return err
		}
	}
	return nil
}

func (v *Service) Inject(app *v1alpha1.Application) error {

	return nil
}

func (v *Service) Setup(client client.Client, scheme *runtime.Scheme) {
	v.Client = client
	v.Scheme = scheme
}

func (v *Service) Name() string {
	return "service"
}

func (v *Service) Clean(app *v1alpha1.Application) error {
	return nil
}
