package service

import (
	appv1alpha1 "gitlab.leinao.ai/application-controller/apis/application/v1alpha1"
)

var app *appv1alpha1.Application
var svc *Service

// func init() {
// 	app = &appv1alpha1.Application{
// 		ObjectMeta: metav1.ObjectMeta{
// 			Name:      "llm-predict",
// 			Namespace: "llm-example",
// 		},

// 		Spec: appv1alpha1.ApplicationSpec{
// 			Scheduler: appv1alpha1.SchedulerSpec{
// 				SchedulerName: appv1alpha1.KubeScheduler,
// 			},
// 			Server: map[string][]appv1alpha1.ServerSpec{
// 				"image-describe": {
// 					{
// 						Version:  "v1",
// 						Replicas: pointer.Int32Ptr(1),
// 						Template: corev1.PodTemplateSpec{
// 							ObjectMeta: metav1.ObjectMeta{
// 								Labels: map[string]string{"app": "image-describe"},
// 							},
// 							Spec: corev1.PodSpec{
// 								Containers: []corev1.Container{
// 									{
// 										Name:            "image-describe",
// 										Image:           "registry.cn-hangzhou.aliyuncs.com/ruanxingbaozi/pytorch:image_recognition_demo",
// 										ImagePullPolicy: corev1.PullAlways,
// 										Args:            []string{"bash", "-c", "python image_recognition_server.py"},
// 										Ports: []corev1.ContainerPort{
// 											{
// 												Name:          "http",
// 												ContainerPort: 5000,
// 											},
// 										},
// 									},
// 								},
// 							},
// 						},
// 					},
// 				},
// 			},
// 		},
// 	}
// 	svc = &Service{}
// 	var scheme = runtime.NewScheme()
// 	utilruntime.Must(clientgoscheme.AddToScheme(scheme))
// 	utilruntime.Must(appv1alpha1.AddToScheme(scheme))

// 	objs := []client.Object{
// 		app,
// 	}
// 	svc.Setup(
// 		fake.NewClientBuilder().WithScheme(scheme).WithObjects(objs...).Build(),
// 		scheme,
// 	)
// }

// func Test_Default(t *testing.T) {
// 	err := svc.Configure(app)
// 	if err != nil {
// 		t.Errorf("Unexpected error: %v", err)
// 	}

// }
