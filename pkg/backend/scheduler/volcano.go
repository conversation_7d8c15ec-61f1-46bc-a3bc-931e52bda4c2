package scheduler

import (
	"context"
	"fmt"

	"gitlab.leinao.ai/application-controller/apis/application/v1alpha1"
	k8serrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/klog/v2"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"
	vcbatchv1 "volcano.sh/apis/pkg/apis/batch/v1alpha1"
	scheduling "volcano.sh/apis/pkg/apis/scheduling/v1beta1"
)

const (
	VolcanoSchedulerName = "volcano"
	ResourcePoolKey      = "resourcepool.system.hero.ai"
)

type VolcanoScheduler struct {
	client.Client
	*runtime.Scheme
}

func (v *VolcanoScheduler) Configure(app *v1alpha1.Application) error {
	// TODO 设置和测试task minMember，保证应用下的每个服务至少有一个副本可用
	minTaskMember := make(map[string]int32)
	for name, servers := range app.Spec.Server {
		for _, server := range servers {
			if server.Replicas != nil && *server.Replicas > 0 {
				minTaskMember[fmt.Sprintf("%s-%s", name, server.Version)] = 1
			}
		}
	}
	podGroup := &scheduling.PodGroup{
		ObjectMeta: metav1.ObjectMeta{
			Name:      app.Name,
			Namespace: app.Namespace,
		},
		Spec: scheduling.PodGroupSpec{
			MinMember:     *app.Spec.Scheduler.MinAvailable,
			MinTaskMember: nil,
			Queue:         app.Spec.Scheduler.Queue,
			MinResources:  nil,
		},
	}
	err := controllerutil.SetControllerReference(app, podGroup, v.Scheme)
	if err != nil {
		return err
	}
	err = v.Create(context.Background(), podGroup)
	if err != nil {
		if k8serrors.IsAlreadyExists(err) {
			return nil
		}
		return err
	}
	return nil
}

func (v *VolcanoScheduler) Name() string {
	return VolcanoSchedulerName
}

func (v *VolcanoScheduler) Inject(app *v1alpha1.Application) error {
	if app.Spec.Scheduler.MinAvailable == nil {
		injectScheduler(app)
	}
	injectServer(app)
	return nil
}

func injectScheduler(app *v1alpha1.Application) {
	var minAvailable int32
	for _, servers := range app.Spec.Server {
		for _, server := range servers {
			if server.Replicas == nil {
				minAvailable++
			} else {
				minAvailable += *server.Replicas
			}
		}
	}
	app.Spec.Scheduler.MinAvailable = &minAvailable
}

func injectServer(app *v1alpha1.Application) {
	for name, servers := range app.Spec.Server {
		for i := range servers {
			if servers[i].Template.Annotations == nil {
				servers[i].Template.Annotations = make(map[string]string)
			}
			if servers[i].Template.Labels == nil {
				servers[i].Template.Labels = make(map[string]string)
			}
			if servers[i].Template.Spec.NodeSelector == nil {
				servers[i].Template.Spec.NodeSelector = make(map[string]string)
			}
			serverName := fmt.Sprintf("%s-%s", name, servers[i].Version)
			servers[i].Template.Annotations[scheduling.KubeGroupNameAnnotationKey] = app.Name
			servers[i].Template.Annotations[vcbatchv1.JobNameKey] = app.Name
			servers[i].Template.Annotations[vcbatchv1.TaskSpecKey] = serverName
			servers[i].Template.Annotations[vcbatchv1.JobVersion] = "0"
			servers[i].Template.Annotations[vcbatchv1.QueueNameKey] = app.Spec.Scheduler.Queue
			servers[i].Template.Annotations[vcbatchv1.PodTemplateKey] = fmt.Sprintf("%s-%s", app.Name, serverName)

			servers[i].Template.Labels[ResourcePoolKey] = app.Spec.Scheduler.Queue
			servers[i].Template.Spec.NodeSelector[ResourcePoolKey] = app.Spec.Scheduler.Queue
			servers[i].Template.Spec.SchedulerName = VolcanoSchedulerName
		}
	}
}

func (v *VolcanoScheduler) Setup(client client.Client, scheme *runtime.Scheme) {
	v.Client = client
	v.Scheme = scheme
}

// kubectl get events -n hero-user --field-selector involvedObject.name=a12684529751420928771198
func (v *VolcanoScheduler) Clean(app *v1alpha1.Application) error {

	err := v.Delete(context.Background(), &scheduling.PodGroup{
		ObjectMeta: metav1.ObjectMeta{
			Name:      app.Name,
			Namespace: app.Namespace,
		},
	})
	if err != nil {
		// 如果是资源未找到的错误，认为删除成功（已经不存在）
		if k8serrors.IsNotFound(err) {
			klog.Infof("PodGroup %s not found", app.Name)
			return nil
		}
		klog.Errorf("Delete PodGroup %s failed，the err is %s", app.Name, err)
		return err
	}
	return nil
}
