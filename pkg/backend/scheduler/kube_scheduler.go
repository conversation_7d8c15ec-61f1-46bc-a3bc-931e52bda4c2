package scheduler

import (
	"gitlab.leinao.ai/application-controller/apis/application/v1alpha1"
	"k8s.io/apimachinery/pkg/runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

const (
	DefaultSchedulerName = "default-scheduler"
)

type DefaultScheduler struct {
}

func (v *DefaultScheduler) Configure(app *v1alpha1.Application) error {
	for _, servers := range app.Spec.Server {
		for _, server := range servers {
			server.Template.Spec.SchedulerName = DefaultSchedulerName
		}
	}
	return nil
}

func (v *DefaultScheduler) Name() string {
	return DefaultSchedulerName
}

func (v *DefaultScheduler) Inject(app *v1alpha1.Application) error {
	for _, servers := range app.Spec.Server {
		for _, server := range servers {
			merge(&server.Scheduler, &app.Spec.Scheduler)
		}
	}
	return nil
}

func (v *DefaultScheduler) Setup(client client.Client, scheme *runtime.Scheme) {

}

func merge(v1 *v1alpha1.SchedulerSpec, v2 *v1alpha1.SchedulerSpec) {
	if v1.Queue == "" {
		v1.Queue = v2.Queue
	}
	if v1.TopologyPolicy == "" {
		v1.TopologyPolicy = v2.TopologyPolicy
	}
}

func (v *DefaultScheduler) Clean(app *v1alpha1.Application) error {
	return nil
}
