package scheduler

import (
	appv1alpha1 "gitlab.leinao.ai/application-controller/apis/application/v1alpha1"
)

var app *appv1alpha1.Application

// func init() {
// 	app = &appv1alpha1.Application{
// 		ObjectMeta: metav1.ObjectMeta{
// 			Name:      "llm-predict",
// 			Namespace: "llm-example",
// 		},

// 		Spec: appv1alpha1.ApplicationSpec{
// 			Scheduler: appv1alpha1.SchedulerSpec{
// 				SchedulerName: appv1alpha1.KubeScheduler,
// 			},
// 			Server: map[string][]appv1alpha1.ServerSpec{
// 				"image-describe": {
// 					{
// 						Version:  "v1",
// 						Replicas: pointer.Int32Ptr(1),
// 					},
// 				},
// 			},
// 		},
// 	}
// }

// func Test_Default(t *testing.T) {
// 	kube := &DefaultScheduler{}
// 	err := kube.Inject(app)
// 	if err != nil {
// 		t.<PERSON><PERSON><PERSON>("Unexpected error: %v", err)
// 	}

// 	err = kube.Configure(app)
// 	if err != nil {
// 		t.Errorf("Unexpected error: %v", err)
// 	}

// 	name := kube.Name()
// 	if name != DefaultSchedulerName {
// 		t.Errorf("Unexpected result: %v", name)
// 	}
// }

// func Test_volcano(t *testing.T) {
// 	vs := &VolcanoScheduler{}
// 	var scheme = runtime.NewScheme()
// 	utilruntime.Must(batch.AddToScheme(scheme))
// 	utilruntime.Must(appv1alpha1.AddToScheme(scheme))
// 	utilruntime.Must(scheduling.AddToScheme(scheme))

// 	var num int32 = 1
// 	app = &appv1alpha1.Application{
// 		ObjectMeta: metav1.ObjectMeta{
// 			Name: "app",
// 		},

// 		Spec: appv1alpha1.ApplicationSpec{
// 			Scheduler: appv1alpha1.SchedulerSpec{
// 				SchedulerName: VolcanoSchedulerName,
// 				MinAvailable:  &num,
// 				Queue:         "app",
// 			},
// 			Server: map[string][]appv1alpha1.ServerSpec{
// 				"image-describe": {
// 					{
// 						Replicas: pointer.Int32Ptr(1),
// 						Template: corev1.PodTemplateSpec{
// 							Spec: corev1.PodSpec{
// 								Containers: []corev1.Container{
// 									{
// 										Name:            "llm-inference-app",
// 										Image:           "registry.cn-hangzhou.aliyuncs.com/ruanxingbaozi/pytorch:image_recognition_demo",
// 										ImagePullPolicy: corev1.PullAlways,
// 										Args:            []string{"bash", "-c", "python app.py"},
// 										Ports: []corev1.ContainerPort{
// 											{
// 												Name:          "http",
// 												ContainerPort: 7860,
// 											},
// 										},
// 									},
// 								},
// 							},
// 						},
// 					},
// 				},
// 			},
// 		},
// 	}
// 	podGroup := &scheduling.PodGroup{
// 		ObjectMeta: metav1.ObjectMeta{
// 			Name:      app.Name,
// 			Namespace: app.Namespace,
// 		},
// 		Spec: scheduling.PodGroupSpec{
// 			MinMember:     *app.Spec.Scheduler.MinAvailable,
// 			MinTaskMember: nil,
// 			Queue:         app.Spec.Scheduler.Queue,
// 			MinResources:  nil,
// 		},
// 	}
// 	objs := []client.Object{
// 		podGroup, app,
// 	}
// 	vs.Setup(
// 		fake.NewClientBuilder().WithScheme(scheme).WithObjects(objs...).Build(),
// 		scheme,
// 	)
// 	err := vs.Inject(app)
// 	if err != nil {
// 		t.Errorf("Unexpected error: %v", err)
// 	}

// 	err = vs.Configure(app)
// 	if err != nil {
// 		t.Errorf("Unexpected error: %v", err)
// 	}
// }
