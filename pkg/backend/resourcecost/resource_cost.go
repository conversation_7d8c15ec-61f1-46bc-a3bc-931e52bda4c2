package resourcecost

import (
	"context"

	"gitlab.leinao.ai/application-controller/apis/application/v1alpha1"
	k8serrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"
)

const (
	DefaultResourceCostName = "default"
)

type ResourceCost struct {
	client.Client
	*runtime.Scheme
}

func (r *ResourceCost) Configure(app *v1alpha1.Application) error {
	rc := &v1alpha1.ResourceCost{
		ObjectMeta: metav1.ObjectMeta{
			Labels:    app.GetLabels(),
			Name:      app.Name,
			Namespace: app.Namespace,
		},
		Spec: v1alpha1.ResourceCostSpec{},
	}
	err := controllerutil.SetControllerReference(app, rc, r.Scheme)
	if err != nil {
		return err
	}
	err = r.Create(context.Background(), rc)
	if err != nil {
		if k8serrors.IsAlreadyExists(err) {
			return nil
		}
		return err
	}
	return nil
}

func (r *ResourceCost) Name() string {
	return "resource-cost"
}

func (r *ResourceCost) Inject(app *v1alpha1.Application) error {
	return nil
}

func (r *ResourceCost) Setup(client client.Client, scheme *runtime.Scheme) {
	r.Client = client
	r.Scheme = scheme
}

func (r *ResourceCost) Clean(app *v1alpha1.Application) error {
	return nil
}
