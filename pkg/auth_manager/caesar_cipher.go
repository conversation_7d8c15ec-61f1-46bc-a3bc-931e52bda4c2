package authmanager

import (
	"strings"
)

// CaesarCipher 加解密密
func CaesarCipher(s string, offset int) string {
	var cipher strings.Builder

	for i := 0; i < len(s); i++ {
		char := rune(s[i])
		if char >= 'a' && char <= 'z' {
			if offset > 0 {
				char += rune((offset + i) % 26)
			} else {
				char += rune((offset - i) % 26)
			}

			if char < 'a' {
				char += 26 // 向左超界
			}
			if char > 'z' {
				char -= 26 // 向右超界
			}
		} else if char >= 'A' && char <= 'Z' {
			if offset > 0 {
				char += rune((offset + i) % 26)
			} else {
				char += rune((offset - i) % 26)
			}

			if char < 'A' {
				char += 26
			}
			if char > 'Z' {
				char -= 26
			}
		}

		cipher.WriteRune(char)
	}

	return cipher.String()
}
