package authmanager

import (
	"fmt"
	"sync"
	"time"

	"github.com/go-resty/resty/v2"
	"k8s.io/klog"
)

type AuthManager struct {
	client       *resty.Client
	authURL      string
	isExpire     bool // 证书过期状态
	isExpireLock sync.Mutex
}

var authOnce sync.Once

var authManager *AuthManager

type AuthResponse struct {
	Code   int                    `json:"code,omitempty"`
	Reason string                 `json:"reason,omitempty"`
	Data   map[string]interface{} `json:"data,omitempty"`
}

type ClientErr struct {
	errMsg string
}

func (c *ClientErr) Error() string {
	return c.errMsg
}

func NewAuthManager(licenseServiceURL string) *AuthManager {
	authOnce.Do(func() {
		if licenseServiceURL == "" {
			panic("license-service-url is not set")
		}
		client := resty.New()
		client.SetTimeout(time.Second * 60)
		authManager = &AuthManager{
			client:  client,
			authURL: licenseServiceURL,
		}
		// 先获取一次校验状态
		authManager.checkLicenseExpire()
	})

	return authManager
}

func (s *AuthManager) RunTask() {
	for {
		expire, err := s.checkLicenseExpire()
		if err != nil {
			klog.Errorf("checkLicenseExpire failed, %s", err.Error())
		}
		s.isExpireLock.Lock()
		s.isExpire = expire
		s.isExpireLock.Unlock()
		if expire {
			// 证书过期快速查询
			time.Sleep(time.Minute * 1)
			continue
		}
		// 正常1小时查询一次
		time.Sleep(time.Hour)
	}
}

func (s *AuthManager) checkLicenseExpire() (bool, error) {
	r := &AuthResponse{}
	authURL := s.authURL + "/license/check/expire"
	resp, err := s.client.R().
		SetHeader("Content-Type", "application/json").
		SetResult(r).
		Get(authURL)

	if err != nil || !resp.IsSuccess() {
		return false, &ClientErr{
			errMsg: fmt.Sprintf("code=%d, authUrl=%s request failed", resp.StatusCode(), authURL),
		}
	}
	if r.Code != 0 {
		return false, &ClientErr{
			errMsg: fmt.Sprintf("code=%d, reason=%s", r.Code, r.Reason),
		}
	}
	if r.Data == nil {
		return false, &ClientErr{
			errMsg: fmt.Sprintf("code=%d, reason=%s", r.Code, "data is nil."),
		}
	}
	value, ok := r.Data["expire"]
	if !ok {
		return false, &ClientErr{
			errMsg: fmt.Sprintf("code=%d, reason=%s", r.Code, "expire is nil."),
		}
	}
	encrypt, ok := value.(string)
	if !ok {
		return false, &ClientErr{
			errMsg: fmt.Sprintf("code=%d, reason=%s", r.Code, "expire is not bool."),
		}
	}
	// 解密
	decrypt := CaesarCipher(encrypt, -3)
	if len(decrypt) <= 10 {
		return false, &ClientErr{
			errMsg: fmt.Sprintf("code=%d, reason=%s", r.Code, "decrypt error."),
		}
	}
	// 获取参数内容
	isExpire := decrypt[5 : len(decrypt)-5]
	if isExpire == "true" {
		return true, nil
	}
	return false, nil
}

func (s *AuthManager) IsLicenseExpire() bool {
	s.isExpireLock.Lock()
	defer s.isExpireLock.Unlock()
	return s.isExpire
}
