/*
Copyright 2023.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package v1alpha1

import (
	"fmt"
	"strconv"

	authmanager "gitlab.leinao.ai/application-controller/pkg/auth_manager"

	"gitlab.leinao.ai/application-controller/pkg/config"

	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/util/validation/field"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/webhook"
	"sigs.k8s.io/controller-runtime/pkg/webhook/admission"
)

const (
	ErrorMsgTriggersMustBeSet               = "open autoscaling: triggers must be set"
	ErrorMsgKedaThresholdValueMustNotBeNull = "keda autoscaling threshold value must not be null"
	ErrorMsgMetadataKey                     = "keda autoscaling trigger matadate must have the key type"
	ErrorMsgUtilizationRequired             = "autoscaling with CPU or memory trigger must have 'Utilization' type"
	ErrorMsgValidMetricNameRequired         = "prometheus trigger must have a valid 'metricName': 'http_requests_total', 'gpu_util', or 'gpu_mem_util'"
	ErrorMsgThresholdRequired               = "prometheus trigger must have a 'threshold'"
	ErrorMsgMinReplicasRequired             = "autoscaling must set 'MinReplicas' and 'MaxReplicas', and 'MinReplicas' must be more than zero"
	ErrorMsgMinLessThanMax                  = "'MinReplicas' must be less than 'MaxReplicas'"
	ErrorMsgReplicasInRange                 = "'MinReplicas' <= 'server.Replicas' < 'MaxReplicas' must be true"
	ErrorMsgServerlessType                  = "serverless type must be 'prometheus'"
	ErrorMsgServerlessMetricName            = "serverless key should contains 'metricName' and  must be 'http_requests_total'"
	ErrorMsgServerlessThreshold             = "serverless key should contains 'threshold' and must be '20'"
)

func (r *Application) SetupWebhookWithManager(mgr ctrl.Manager) error {
	return ctrl.NewWebhookManagedBy(mgr).
		For(r).
		Complete()
}

// TODO(user): EDIT THIS FILE!  THIS IS SCAFFOLDING FOR YOU TO OWN!

//+kubebuilder:webhook:path=/mutate-system-hero-ai-v1alpha1-application,mutating=true,failurePolicy=fail,sideEffects=None,groups=system.hero.ai,resources=applications,verbs=create;update,versions=v1alpha1,name=mapplication.kb.io,admissionReviewVersions=v1

var _ webhook.Defaulter = &Application{}

// Default implements webhook.Defaulter so a webhook will be registered for the type
func (r *Application) Default() {
	if r.Spec.Scheduler.Queue == "" {
		r.Spec.Scheduler.Queue = "default"
	}
	// TODO(user): fill in your defaulting logic.

}

// TODO(user): change verbs to "verbs=create;update;delete" if you want to enable deletion validation.
//+kubebuilder:webhook:path=/validate-system-hero-ai-v1alpha1-application,mutating=false,failurePolicy=fail,sideEffects=None,groups=system.hero.ai,resources=applications,verbs=create;update,versions=v1alpha1,name=vapplication.kb.io,admissionReviewVersions=v1

var _ webhook.Validator = &Application{}

// ValidateCreate implements webhook.Validator so a webhook will be registered for the type
func (r *Application) ValidateCreate() (admission.Warnings, error) {

	// TODO(user): fill in your validation logic upon object creation.
	return r.Validate()
}

// ValidateUpdate implements webhook.Validator so a webhook will be registered for the type
func (r *Application) ValidateUpdate(old runtime.Object) (admission.Warnings, error) {

	// TODO(user): fill in your validation logic upon object update.
	return r.Validate()
}

// ValidateDelete implements webhook.Validator so a webhook will be registered for the type
func (r *Application) ValidateDelete() (admission.Warnings, error) {
	// 校验证书是否过期，若过期拒绝处理
	expire := authmanager.NewAuthManager(config.SC.LicenseServiceURL).IsLicenseExpire()
	if expire {
		warnings := make(admission.Warnings, 0)
		return warnings, &field.Error{
			Type:   field.ErrorTypeInternal,
			Field:  "spec",
			Detail: "license has expired.",
		}
	}
	// TODO(user): fill in your validation logic upon object deletion.
	return nil, nil
}

func (r *Application) Validate() (admission.Warnings, error) {
	r.validateTriggers()
	r.ValidateServiceMesh()
	return nil, nil
}

func (r *Application) validateTriggers() (admission.Warnings, error) {
	if r.Spec.AutoScaling == nil {
		return nil, nil
	}
	for trigger, value := range r.Spec.AutoScaling.ScalingTriggers {
		switch trigger {
		case "cpuUtil":
			valueInt, err := strconv.Atoi(value)
			if valueInt < 0 || valueInt > 100 || err != nil {
				return nil, &field.Error{
					Type:   field.ErrorTypeRequired,
					Field:  "spec.autoScaling.scalingTriggers.cpuUtil",
					Detail: "cpuUtil must be in the range of 0 to 100",
				}
			}
		case "memAvg":
		case "reqSec":
		case "gpuUtil":
			valueInt, err := strconv.Atoi(value)
			if valueInt < 0 || valueInt > 100 || err != nil {
				return nil, &field.Error{
					Type:   field.ErrorTypeRequired,
					Field:  "spec.autoScaling.scalingTriggers.cpuUtil",
					Detail: "cpuUtil must be in the range of 0 to 100",
				}
			}
		case "gpuMem":
		default:
			return nil, &field.Error{
				Type:   field.ErrorTypeRequired,
				Field:  "spec.autoScaling.scalingTriggers",
				Detail: fmt.Sprintf("unsuported trigger: %s ", trigger),
			}
		}

	}
	return nil, nil
}

func (r *Application) ValidateServiceMesh() (admission.Warnings, error) {
	for name, servers := range r.Spec.Server {
		if len(servers) == 0 {
			return nil, &field.Error{
				Type:   field.ErrorTypeRequired,
				Field:  "spec.server." + name,
				Detail: "server must have at least one element",
			}
		}
		// TODO
	}
	return nil, nil

}
