/*
Copyright 2023.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package v1alpha1

import (
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	authmanager "gitlab.leinao.ai/application-controller/pkg/auth_manager"

	"gitlab.leinao.ai/application-controller/pkg/config"

	k8serrors "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/apimachinery/pkg/util/validation/field"
	ctrl "sigs.k8s.io/controller-runtime"
	logf "sigs.k8s.io/controller-runtime/pkg/log"
	"sigs.k8s.io/controller-runtime/pkg/webhook"
	"sigs.k8s.io/controller-runtime/pkg/webhook/admission"
)

const (
	ErrorMsgTriggersMustBeSet               = "open autoscaling: triggers must be set"
	ErrorMsgKedaThresholdValueMustNotBeNull = "keda autoscaling threshold value must not be null"
	ErrorMsgMetadataKey                     = "keda autoscaling trigger matadate must have the key type"
	ErrorMsgUtilizationRequired             = "autoscaling with CPU or memory trigger must have 'Utilization' type"
	ErrorMsgValidMetricNameRequired         = "prometheus trigger must have a valid 'metricName': 'http_requests_total', 'gpu_util', or 'gpu_mem_util'"
	ErrorMsgThresholdRequired               = "prometheus trigger must have a 'threshold'"
	ErrorMsgMinReplicasRequired             = "autoscaling must set 'MinReplicas' and 'MaxReplicas', and 'MinReplicas' must be more than zero"
	ErrorMsgMinLessThanMax                  = "'MinReplicas' must be less than 'MaxReplicas'"
	ErrorMsgReplicasInRange                 = "'MinReplicas' <= 'server.Replicas' < 'MaxReplicas' must be true"
	ErrorMsgServerlessType                  = "serverless type must be 'prometheus'"
	ErrorMsgServerlessMetricName            = "serverless key should contains 'metricName' and  must be 'http_requests_total'"
	ErrorMsgServerlessThreshold             = "serverless key should contains 'threshold' and must be '20'"
)

// log is for logging in this package.
var applicationlog = logf.Log.WithName("application-resource")

func (r *Application) SetupWebhookWithManager(mgr ctrl.Manager) error {
	return ctrl.NewWebhookManagedBy(mgr).
		For(r).
		Complete()
}

// TODO(user): EDIT THIS FILE!  THIS IS SCAFFOLDING FOR YOU TO OWN!

//+kubebuilder:webhook:path=/mutate-system-hero-ai-v1alpha1-application,mutating=true,failurePolicy=fail,sideEffects=None,groups=system.hero.ai,resources=applications,verbs=create;update,versions=v1alpha1,name=mapplication.kb.io,admissionReviewVersions=v1

var _ webhook.Defaulter = &Application{}

// Default implements webhook.Defaulter so a webhook will be registered for the type
func (r *Application) Default() {
	if r.Spec.Gateway != nil && r.Spec.Gateway.Backend == "" {
		r.Spec.Gateway.Backend = IstioGateway
	}

	// TODO(user): fill in your defaulting logic.
	//autoscaling default
	for _, servers := range r.Spec.Server {
		for i := range servers {
			// 直接使用指针修改  //如果server里配置了，就直接使用server的配置，没配置就把application里的赋值过来

			//每个子服务的autoscaling为nil,因为二者必须配置了一个才能走进来，所以只需要考虑单个等于nil的情况
			//需要考虑空指针问题
			//1.每个子服务不为nil,但是全局的app.Spec.AutoScaling为nil，这种情况直接return
			if r.Spec.AutoScaling == nil {
				return
			}
			servers[i].AutoScaling = merge(servers[i].AutoScaling, r.Spec.AutoScaling)
		}
	}
}

func merge(v1, v2 *AutoScalingSpec) *AutoScalingSpec {
	//如果 v1 是 nil，直接返回 v2
	if v1 == nil {
		return v2
	}
	merged := *v1
	if merged.CooldownPeriod == nil {
		merged.CooldownPeriod = v2.CooldownPeriod
	}
	if merged.Triggers == nil {
		merged.Triggers = v2.Triggers
	}

	if merged.Serverless == nil {
		merged.Serverless = v2.Serverless
	}
	if merged.MinReplicas == nil {
		merged.MinReplicas = v2.MinReplicas
	}
	if merged.MaxReplicas == nil {
		merged.MaxReplicas = v2.MaxReplicas
	}

	if merged.Backend == "" {
		merged.Backend = v2.Backend
	}
	// 返回合并后的结果
	return &merged
}

// TODO(user): change verbs to "verbs=create;update;delete" if you want to enable deletion validation.
//+kubebuilder:webhook:path=/validate-system-hero-ai-v1alpha1-application,mutating=false,failurePolicy=fail,sideEffects=None,groups=system.hero.ai,resources=applications,verbs=create;update,versions=v1alpha1,name=vapplication.kb.io,admissionReviewVersions=v1

var _ webhook.Validator = &Application{}

// ValidateCreate implements webhook.Validator so a webhook will be registered for the type
func (r *Application) ValidateCreate() (admission.Warnings, error) {

	// TODO(user): fill in your validation logic upon object creation.
	return r.Validate()
}

// ValidateUpdate implements webhook.Validator so a webhook will be registered for the type
func (r *Application) ValidateUpdate(old runtime.Object) (admission.Warnings, error) {

	// TODO(user): fill in your validation logic upon object update.
	return r.Validate()
}

// ValidateDelete implements webhook.Validator so a webhook will be registered for the type
func (r *Application) ValidateDelete() (admission.Warnings, error) {
	// 校验证书是否过期，若过期拒绝处理
	expire := authmanager.NewAuthManager(config.SC.LicenseServiceURL).IsLicenseExpire()
	if expire {
		warnings := make(admission.Warnings, 0)
		return warnings, &field.Error{
			Type:   field.ErrorTypeInternal,
			Field:  "spec",
			Detail: "license has expired.",
		}
	}
	// TODO(user): fill in your validation logic upon object deletion.
	return nil, nil
}

func (r *Application) Validate() (admission.Warnings, error) {
	var (
		warnings = make(admission.Warnings, 0)
		allErrs  field.ErrorList
	)

	// Check if the license has expired.
	if expire := checkLicense(); expire {
		return warnings, newLicenseExpiredError()
	}

	// 校验证书是否过期，若过期拒绝处理
	// Check if the license has expired.
	if expire := checkLicense(); expire {
		applicationlog.Info("license has expired.")
		return warnings, newLicenseExpiredError()
	}

	warnings, allErrs = ValidateServiceMeshDuration(r.Spec.ServiceMesh)

	for name, ser := range r.Spec.Server {
		if len(ser) == 0 {
			allErrs = append(allErrs, field.Invalid(field.NewPath("spec").Child("server", "template"),
				len(ser),
				// 使用日志记录
				fmt.Sprintf("server: %s must be at least 1 template", name)))
			warnings = append(warnings, fmt.Sprintf("server: %s must be at least 1 template", name))
			continue
		}
		var sum int32
		for _, server := range ser {
			warns, errs := ValidateServiceMeshDuration(server.ServiceMesh)
			if len(errs) > 0 {
				allErrs = append(allErrs, errs...)
				warnings = append(warnings, warns...)
			}
			weight := getWeight(server)

			if len(ser) == 1 {
				if (0 < weight && weight < 100) || weight > 100 {
					allErrs = append(allErrs, field.Invalid(field.NewPath("spec").Child("server", "serviceMesh", "subRout", "weight"),
						sum,
						fmt.Sprintf("server: %s weight sum must be equal to 100", name)))
					warnings = append(warnings, fmt.Sprintf("server: %s weight sum must be equal to 100", name))
				}

			} else {
				sum += weight
			}
			//	检验service里使用的port 及containers的长度
			validateServerContainers(name, &server, &allErrs, &warnings)

			//   校验autoscaling的参数,当只开启弹性伸缩，不开启serverless的时候，minReplica和maxReplica必传
			validateAutoScaling(name, server, &allErrs, &warnings)
		}

		validateWeightSum(name, sum, len(ser), &allErrs, &warnings)
	}
	if len(allErrs) > 0 {
		return warnings, k8serrors.NewInvalid(
			schema.GroupKind{Group: "system.hero.ai", Kind: "Application"},
			r.Name,
			allErrs)
	}
	return nil, nil
}

func validateWeightSum(name string, sum int32, count int, allErrs *field.ErrorList, warnings *admission.Warnings) {
	if count > 1 && (sum != 100) {
		errPath := field.NewPath("spec").Child("server", "serviceMesh", "subRoute", "weight")
		errMsg := fmt.Sprintf("server: %s weight sum must be equal to 100", name)
		*allErrs = append(*allErrs, field.Invalid(errPath, sum, errMsg))
		*warnings = append(*warnings, errMsg)
	}
}
func validateServerContainers(name string, server *ServerSpec, allErrs *field.ErrorList, warnings *admission.Warnings) {
	if len(server.Template.Spec.Containers) <= 0 {
		errPath := field.NewPath("spec").Child("server", "Template", "Spec", "Containers")
		errMsg := fmt.Sprintf("server: %s Template containers len must be more than 0", name)
		*allErrs = append(*allErrs, field.Invalid(errPath, len(server.Template.Spec.Containers), errMsg))
		*warnings = append(*warnings, errMsg)
	}
}
func getWeight(server ServerSpec) int32 {
	if server.ServiceMesh != nil && server.ServiceMesh.SubRoute != nil && server.ServiceMesh.SubRoute.Weight != nil {
		return *server.ServiceMesh.SubRoute.Weight
	}
	return 0
}
func checkLicense() bool {
	expire := authmanager.NewAuthManager(config.SC.LicenseServiceURL).IsLicenseExpire()
	return expire
}

func newLicenseExpiredError() *field.Error {
	return &field.Error{
		Type:   field.ErrorTypeInternal,
		Field:  "spec",
		Detail: "license has expired.",
	}
}

func ValidateServiceMeshDuration(sm *ServiceMeshSpec) (admission.Warnings, field.ErrorList) {
	var (
		warnings = make(admission.Warnings, 0)
		allErrs  field.ErrorList
	)
	if sm == nil || sm.SubRoute == nil {
		return warnings, allErrs
	}
	if retries := sm.SubRoute.Retries; retries != nil && retries.AutoRetire {
		if retries.Attempts <= 0 {
			path := field.NewPath("subRoute").Child("retries", "attempts")
			errMsg := "retries: attempts must be greater than 0"
			allErrs = append(allErrs, field.Invalid(path, retries.Attempts, errMsg))
			warnings = append(warnings, errMsg)
		}
		if retries.PerTryTimeout != nil {
			if err := ValidateDuration(retries.PerTryTimeout.Duration); err != nil {
				path := field.NewPath("subRoute").Child("retries", "perTryTimeout")
				errMsg := fmt.Sprintf("perTryTimeout: %s - %s", retries.PerTryTimeout.Duration.String(), err.Error())
				allErrs = append(allErrs, field.Invalid(path, retries.PerTryTimeout.Duration.String(), errMsg))
				warnings = append(warnings, errMsg)
			}
		}
	}
	var tp = sm.SubRoute.TrafficPolicy
	if tp == nil {
		return warnings, allErrs
	}

	if cb := tp.CircuitBreaker; cb != nil {
		if cb.Interval != nil {
			if err := ValidateDuration(cb.Interval.Duration); err != nil {
				path := field.NewPath("subRoute").Child("trafficPolicy", "circuitBreaker", "interval")
				errMsg := fmt.Sprintf("interval: %s - %s", cb.Interval.Duration.String(), err.Error())
				allErrs = append(allErrs, field.Invalid(path, cb.Interval.Duration.String(), errMsg))
				warnings = append(warnings, errMsg)
			}
		}
		if cb.BaseEjectionTime != nil {
			if err := ValidateDuration(cb.BaseEjectionTime.Duration); err != nil {
				path := field.NewPath("subRoute").Child("trafficPolicy", "circuitBreaker", "baseEjectionTime")
				errMsg := fmt.Sprintf("baseEjectionTime: %s - %s", cb.BaseEjectionTime.Duration.String(), err.Error())
				allErrs = append(allErrs, field.Invalid(path, cb.BaseEjectionTime.Duration.String(), errMsg))
				warnings = append(warnings, errMsg)
			}
		}
	}
	return warnings, allErrs
}

func ValidateDuration(pd time.Duration) error {
	if len(strings.Split(pd.String(), ".")) > 1 {
		return errors.New("duration cannot be of floating point type")
	}
	if pd < time.Millisecond {
		return errors.New("duration must be greater than 1ms")
	}
	if pd%time.Millisecond != 0 {
		return errors.New("only durations to ms precision are supported")
	}
	return nil
}

func validateAutoScaling(serverName string, server ServerSpec, allErrs *field.ErrorList, warnings *admission.Warnings) {
	//开启弹性伸缩不开启serverless
	if server.AutoScaling == nil || (server.AutoScaling.Backend == "keda" && server.AutoScaling.Triggers == nil && server.AutoScaling.Serverless == nil) {
		return
	}

	path := field.NewPath("spec").Child("server", "AutoScaling")
	//开启弹性伸缩不开启serverless
	if server.AutoScaling.Serverless == nil {
		validateTriggers(serverName, server, allErrs, warnings, path.Child("MinReplicas||MaxReplicas||triggers"))

		validateReplicaSettings(serverName, server, allErrs, warnings, path.Child("Replicas"))
		//	开启serverless
	} else if server.AutoScaling.Serverless != nil {
		//校验serverless下的字段
		validateServerless(serverName, server, allErrs, warnings, path.Child("serverless"))
		//开启serverless又开启弹性伸缩
		if server.AutoScaling.Triggers != nil {
			//   minReplica must be 0,因为代码里已经适配，所以这里可以不校验
			validateReplicaSettings(serverName, server, allErrs, warnings, path.Child("Replicas"))
		}
	}
}

func validateTriggers(serverName string, server ServerSpec, allErrs *field.ErrorList, warnings *admission.Warnings, path *field.Path) {
	//开启弹性伸缩不开启serverless必须配置triggers
	if len(server.AutoScaling.Triggers) == 0 {
		addValidationIssue(allErrs, warnings, path, *server.AutoScaling, serverName, ErrorMsgTriggersMustBeSet)
		return
	}
	//校验trigger下的字段
	for _, trigger := range server.AutoScaling.Triggers {
		validateTrigger(serverName, trigger, allErrs, warnings, path.Child("triggers"))
	}
}

//		    triggers:
//	     - type: cpu
//	       metadata:
//	         type: Utilization
//	         value: "50"
//	     - type: memory
//	       metadata:
//	         type: Utilization
//	         value: "512"  #单位byte
//	     - type: prometheus
//	       metadata:
//	         metricName: http_requests_total
//	         threshold: '20'
//	     - type: prometheus
//	       metadata:
//	         metricName: gpu_util
//	         threshold: '2'   #2%
//	     - type: prometheus
//	       metadata:
//	         metricName: gpu_mem_util
//	         threshold: '10'  #1000Mi
func validateTrigger(serverName string, trigger ScaleTriggers, allErrs *field.ErrorList, warnings *admission.Warnings, path *field.Path) {
	switch trigger.Type {
	case "cpu", "memory":
		validateResourceTrigger(serverName, trigger, allErrs, warnings, path)
	case "prometheus":
		validatePrometheusTrigger(serverName, trigger, allErrs, warnings, path)
	default:
		addValidationIssue(allErrs, warnings, path, trigger.Type, serverName, "unsupported trigger type: "+trigger.Type)
	}
}
func validatePrometheusTrigger(serverName string, trigger ScaleTriggers, allErrs *field.ErrorList, warnings *admission.Warnings, path *field.Path) {
	metricNames := []string{"http_requests_total", "gpu_util", "gpu_mem_util"}
	if metricName, ok := trigger.Metadata["metricName"]; !ok || !contains(metricNames, metricName) {
		addValidationIssue(allErrs, warnings, path, trigger, serverName, ErrorMsgValidMetricNameRequired)
	}

	if _, ok := trigger.Metadata["threshold"]; !ok {
		addValidationIssue(allErrs, warnings, path, trigger, serverName, ErrorMsgThresholdRequired)
	}

	validateMetadataIntValue("threshold", serverName, trigger, allErrs, warnings, path)
}

func validateResourceTrigger(serverName string, trigger ScaleTriggers, allErrs *field.ErrorList, warnings *admission.Warnings, path *field.Path) {
	if typeValue, ok := trigger.Metadata["type"]; !ok {
		addValidationIssue(allErrs, warnings, path, trigger.Metadata, serverName, ErrorMsgMetadataKey)
	} else if typeValue != "Utilization" {
		addValidationIssue(allErrs, warnings, path, typeValue, serverName, ErrorMsgUtilizationRequired)
	}
	if _, ok := trigger.Metadata["value"]; !ok {
		addValidationIssue(allErrs, warnings, path, trigger, serverName, "enabled autoscaling the metadata key must contains key value")
	}
	validateMetadataIntValue("value", serverName, trigger, allErrs, warnings, path)
}

// keda的阈值要是int类型
func validateMetadataIntValue(key, serverName string, trigger ScaleTriggers, allErrs *field.ErrorList, warnings *admission.Warnings, path *field.Path) {
	if value, ok := trigger.Metadata[key]; ok {
		if _, err := strconv.Atoi(value); err != nil {
			addValidationIssue(allErrs, warnings, path, value, serverName, fmt.Sprintf("keda autoscaling %s value must be int", key))
		}
		if value == "" {
			addValidationIssue(allErrs, warnings, path, value, serverName, ErrorMsgKedaThresholdValueMustNotBeNull)
		}
	}
}

func validateReplicaSettings(serverName string, server ServerSpec, allErrs *field.ErrorList, warnings *admission.Warnings, path *field.Path) {
	if server.AutoScaling.MinReplicas == nil || server.AutoScaling.MaxReplicas == nil || *server.AutoScaling.MinReplicas <= 0 {
		addValidationIssue(allErrs, warnings, path, *server.AutoScaling, serverName, ErrorMsgMinReplicasRequired)
	} else if *server.AutoScaling.MinReplicas >= *server.AutoScaling.MaxReplicas {
		addValidationIssue(allErrs, warnings, path, *server.AutoScaling, serverName, ErrorMsgMinLessThanMax)
	} else if server.Replicas == nil {
		if *server.AutoScaling.MinReplicas > 1 || 1 > *server.AutoScaling.MaxReplicas {
			addValidationIssue(allErrs, warnings, path, *server.AutoScaling, serverName, ErrorMsgReplicasInRange)
		}
	} else if *server.AutoScaling.MinReplicas > *server.Replicas || *server.Replicas > *server.AutoScaling.MaxReplicas {
		addValidationIssue(allErrs, warnings, path, *server.AutoScaling, serverName, ErrorMsgReplicasInRange)
	}
}

// serverless:
//   - type: prometheus
//     metadata:
//     metricName: http_requests_total
//     threshold: '20'
func validateServerless(serverName string, server ServerSpec, allErrs *field.ErrorList, warnings *admission.Warnings, path *field.Path) {
	if server.AutoScaling.Serverless[0].Type != "prometheus" {
		addValidationIssue(allErrs, warnings, path, server.AutoScaling.Serverless[0].Type, serverName, ErrorMsgServerlessType)
	}
	if metricName, ok := server.AutoScaling.Serverless[0].Metadata["metricName"]; !ok || metricName != "http_requests_total" {
		addValidationIssue(allErrs, warnings, path, server.AutoScaling.Serverless[0].Metadata, serverName, ErrorMsgServerlessMetricName)
	}
	if threshold, ok := server.AutoScaling.Serverless[0].Metadata["threshold"]; !ok || threshold != "20" {
		addValidationIssue(allErrs, warnings, path, server.AutoScaling.Serverless[0].Metadata, serverName, ErrorMsgServerlessThreshold)
	}
}

func addValidationIssue(allErrs *field.ErrorList, warnings *admission.Warnings, path *field.Path, value interface{}, serverName, message string) {
	detailedMessage := fmt.Sprintf("server: %s %s", serverName, message)
	*allErrs = append(*allErrs, field.Invalid(path, value, detailedMessage))
	*warnings = append(*warnings, detailedMessage)
}
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}
