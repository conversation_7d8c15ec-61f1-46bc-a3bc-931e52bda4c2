package v1alpha1

// +kubebuilder:rbac:groups=system.hero.ai,resources=applications,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=system.hero.ai,resources=applications/status,verbs=get;update;patch
// +kubebuilder:rbac:groups=system.hero.ai,resources=applications/finalizers,verbs=update
// +kubebuilder:rbac:groups=networking.istio.io,resources=gateways;virtualservices;destinationrules,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=apps,resources=deployments,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=core,resources=services;pods;serviceaccounts,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=keda.sh,resources=scaledobjects,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=http.keda.sh,resources=httpscaledobjects,verbs=get;list;watch;create;update;patch;delete
