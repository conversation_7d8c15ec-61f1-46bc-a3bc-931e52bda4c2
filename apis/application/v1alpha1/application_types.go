/*
Copyright 2023.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package v1alpha1

import (
	"time"

	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

const (
	LabelApplicationName    = "leinao.ai/app-name"
	LabelApplicationVersion = "leinao.ai/app-version"
	LabelApplicationJobID   = "job.id"

	LabelAppName    = "app"
	LabelAppversion = "version"
	AppIndexField   = ".metadata.labels['" + LabelApplicationName + "']"
)

// DeploymentMode 表示部署模式（在线推理、多服务编排、分布式推理）
type DeploymentMode string

const (
	// OnlineMode 表示标准在线推理（Serverless 单服务）
	OnlineMode DeploymentMode = "Online"

	// PipelineMode 表示多服务串联编排（例如前后处理+模型服务）
	PipelineMode DeploymentMode = "Pipeline"

	// DistributedMode 表示分布式推理（模型分片、横向扩展等）
	DistributedMode DeploymentMode = "Distributed"
)

type ServerType string

type NumaPolicy string

const (
	DefaultBackend = "default"
)

//+kubebuilder:object:root=true
//+kubebuilder:subresource:status
//+kubebuilder:resource:path=applications,shortName=app
//+kubebuilder:printcolumn:name="Available",type="integer",JSONPath=".status.availableServers"
//+kubebuilder:printcolumn:name="Status",type="string",JSONPath=".status.phase"
//+kubebuilder:printcolumn:name="Age",type="date",JSONPath=".metadata.creationTimestamp"

// Application is the Schema for the applications API
type Application struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec   ApplicationSpec   `json:"spec,omitempty"`
	Status ApplicationStatus `json:"status,omitempty"`
}

// ApplicationSpec defines the desired state of Application
type ApplicationSpec struct {

	// Version is the application version`.
	// +optional
	Version string `json:"version,omitempty" protobuf:"bytes,1,opt,name=version"`

	// DeploymentMode defines the deployment strategy for the application's servers.
	// It determines the mode of service deployment and orchestration.
	// Valid values are:
	// - "Online": Standard online inference (Serverless single service).
	// - "Pipeline": Multi-service serial orchestration (e.g., pre-processing + model service).
	// - "Distributed": Distributed inference (e.g., model sharding, horizontal scaling).
	// +kubebuilder:validation:Enum=Online;Pipeline;Distributed
	// +kubebuilder:default:=Online
	DeploymentMode DeploymentMode `json:"deploymentMode,omitempty" protobuf:"bytes,10,opt,name=deploymentMode"`

	// AutoScaling is the default value of `Server.template.spec.autoScaling`.
	// +optional
	AutoScaling *AutoScalingSpec `json:"autoScaling,omitempty" protobuf:"bytes,3,opt,name=autoScaling"`

	// Scheduler is the default value of `Server.template.spec.scheduler`.
	// +optional
	Scheduler SchedulerSpec `json:"scheduler,omitempty" protobuf:"bytes,6,opt,name=scheduler"`

	// Microservice/Service specifies the service specification of Application
	// +optional
	Server map[string][]ServerSpec `json:"servers,omitempty" protobuf:"bytes,7,opt,name=servers"`
}

// ServerSpec specifies the server specification of Application.
type ServerSpec struct {

	// Version specifies the version of servers
	// +optional
	Version string `json:"version,omitempty" protobuf:"bytes,3,opt,name=version"`

	// Replicas specifies the replicas of this server
	// +optional
	Replicas *int32 `json:"replicas,omitempty" protobuf:"bytes,4,opt,name=replicas"`

	// ServiceMesh is the default value of `service.template.spec.serviceMesh`.
	// +optional
	ServiceMesh *ServiceMeshSpec `json:"serviceMesh,omitempty" protobuf:"bytes,6,opt,name=serviceMesh"`

	// Specifies the pod that will be created for this ServerSpec
	// when executing a Job
	// +optional
	Template v1.PodTemplateSpec `json:"template,omitempty" protobuf:"bytes,8,opt,name=template"`
}

type AutoScalingBackend string

const (
	Keda AutoScalingBackend = "keda"
	//K8sHPA AutoScalingBackend = "hpa"
)

type ServiceMeshBackend string

const (
	Istio ServiceMeshBackend = "istio"
	//Linkerd ServiceMeshBackend = "linkerd"
)

type GatewayBackend string

const (
	IstioGateway GatewayBackend = "istio"
)

type ServerlessBackend string

type SchedulerBackend string

const (
	VolcanoScheduler SchedulerBackend = "volcano"
	KubeScheduler    SchedulerBackend = "default"
)

// AutoScalingSpec defines the specification of AutoScaling.
type AutoScalingSpec struct {

	// MinReplicas controls the minimum number of pod replicas.
	//
	// - To enable **Serverless scale-to-zero**, set `minReplicas = 0`.
	// - To enable **HPA-based autoscaling**, set `minReplicas > 0` and `< maxReplicas`.
	// - If neither feature is enabled, this field can be omitted.
	// +optional
	MinReplicas *int32 `json:"minReplicas,omitempty" protobuf:"varint,2,opt,name=minReplicas"`

	// MaxReplicas is the AutoScaling set deployment max replicas.
	// +optional
	MaxReplicas *int32 `json:"maxReplicas,omitempty" protobuf:"bytes,3,opt,name=maxReplicas"`

	//Specifies the cooldown period in seconds. The cooldown period is the time between automatic scaling actions to prevent multiple consecutive scaling operations in rapid succession.
	//If not provided, the default value will be used.指定自动扩展的冷却期时间（以秒为单位）。冷却期是自动扩展操作之间的等待时间，以确保在连续事件中不会立即触发多次自动扩展 #单位是秒
	// +optional
	CooldownPeriod *int32 `json:"cooldownPeriod,omitempty" protobuf:"bytes,4,opt,name=cooldownPeriod"`

	// ScalingTriggers defines auto-scaling trigger conditions.
	//
	// The key is the metric name, value is the threshold string.
	// Supported keys include:
	//   - "cpuUtil" : CPU utilization percentage (e.g. "50")
	//   - "memAvg"  : average memory usage (e.g. "512Mi")
	//   - "reqSec"  : requests per second (e.g. "20")
	//   - "gpuUtil" : GPU utilization percentage (e.g. "60")
	//   - "gpuMem"  : GPU memory usage (e.g. "1Gi")
	//
	// Example:
	//   scalingTriggers:
	//     cpuUtil: "60"
	//     memAvg: "512Mi"
	//     reqSec: "100"
	//     gpuUtil: "75"
	//     gpuMem: "2Gi"
	//
	// +kubebuilder:validation:Optional
	// +kubebuilder:pruning:PreserveUnknownFields
	// +kubebuilder:validation:Type=object
	// +optional
	ScalingTriggers map[string]string `json:"scalingTriggers,omitempty" protobuf:"bytes,5,rep,name=scalingTriggers"`
}

// ScaleTriggers reference the scaler that will be used
type ScaleTriggers struct {
	//触发器的类型，如prometheus、cron、external、rabbitmq、cpu、memory指标,如果一个prometheus要配置多个查询语句的话就需要配置一个
	// +optional
	Type string `json:"type"`
	//于存储触发器的元数据信息。元数据信息可以提供有关触发器的额外信息，例如触发器的配置参数或描述，eg: Metadata:
	//  query: "sum(container_cpu_usage_seconds_total{namespace='my-namespace'})"
	//可以简化传参，如阈值、指标值类型，然后我们自己适配组装每种类型对应的具体参数配置
	Metadata map[string]string `json:"metadata"`
}

// ServiceMeshSpec defines the specification of ServiceMesh.
type ServiceMeshSpec struct {
	// Gateway is the Gateway project，set none means it will not be opened.
	// +optional
	IngressGateway bool `json:"ingressGateway,omitempty" protobuf:"bytes,2,opt,name=gatewayEnable"`

	// SubRoute Traffic distribution rules and load balancing policies 。
	// +optional
	SubRoute *SubRoute `json:"subRoute,omitempty" protobuf:"bytes,1,opt,name=subRoute"`

	// AutoMTLS configures the service mesh to enable automatic mtls.
	// +kubebuilder:default=true
	// +optional
	AutoMTLS bool `json:"autoMTLS,omitempty" protobuf:"bytes,1,opt,name=autoMTLS"`
}

type SubRoute struct {
	// Weight specifies the relative proportion of traffic to be forwarded to the destination. A destination will receive `weight/(sum of all weights)` requests.
	// If there is only one destination in a rule, it will receive all traffic.
	// Otherwise, if weight is `0`, the destination will not receive any traffic.
	// +kubebuilder:validation:Minimum=1
	// +kubebuilder:validation:Maximum=100
	Weight *int32 `protobuf:"varint,2,opt,name=weight,proto3" json:"weight,omitempty"`

	// Match defines the predicate used to match requests to a
	// +optional
	Match []*HTTPMatchRequest `json:"match,omitempty" protobuf:"bytes,1,opt,name=match"`

	// TimeoutSeconds defines the maximum duration in seconds to wait for an HTTP request before timing out.
	// Only applies to online inference requests.
	//
	// +kubebuilder:validation:Minimum=1
	// +optional
	TimeoutSeconds *int32 `json:"timeoutSeconds,omitempty" protobuf:"varint,3,opt,name=timeoutSeconds"`

	// +optional
	TrafficPolicy *TrafficPolicy `json:"trafficPolicy,omitempty" protobuf:"varint,4,opt,name=trafficPolicy"`
}

type TrafficPolicy struct {
	// Settings controlling the load balancer algorithms.
	// 0: default 2 随机策略 4:轮训策略
	// +kubebuilder:validation:Enum=0;1;2;3;4
	// +optional
	LoadBalancer int32 `json:"loadBalancer,omitempty"`
	// Traffic policies to apply (load balancing policy, connection pool
	// sizes, outlier detection).
	// +optional
	CircuitBreaker *CircuitBreaker `protobuf:"bytes,2,opt,name=circuitBreaker,json=circuitBreaker,proto3" json:"circuitBreaker,omitempty"`
}

type CircuitBreaker struct {
	// Maximum number of HTTP1 /TCP connections to a destination host. Default 2^32-1.
	// +optional
	// +kubebuilder:validation:Maximum=1024
	// +kubebuilder:validation:Minimum=1
	MaxConnections int32 `protobuf:"varint,1,opt,name=maxConnections,json=maxConnections,proto3" json:"maxConnections,omitempty"`

	// Maximum number of pending HTTP requests to a destination. Default 2^32-1.
	// +optional
	// +kubebuilder:validation:Maximum=1024
	// +kubebuilder:validation:Minimum=1
	HTTP1MaxPendingRequests int32 `protobuf:"varint,1,opt,name=http1MaxPendingRequests,json=http1MaxPendingRequests,proto3" json:"http1MaxPendingRequests,omitempty"`

	// Number of errors before a host is ejected from the connection
	// pool. Defaults to 5. When the upstream host is accessed over HTTP, a
	// 502, 503, or 504 return code qualifies as an error. When the upstream host
	// is accessed over an opaque TCP connection, connect timeouts and
	// connection error/failure events qualify as an error.
	// $hide_from_docs
	//
	// Deprecated: Do not use.
	// +optional
	// +kubebuilder:validation:Maximum=1024
	// +kubebuilder:validation:Minimum=1
	ConsecutiveErrors int32 `protobuf:"varint,1,opt,name=consecutiveErrors,json=consecutiveErrors,proto3" json:"consecutiveErrors,omitempty"`

	// Time interval between ejection sweep analysis. format:
	// 1h/1m/1s/1ms. MUST BE >=1ms. Default is 10s.
	// +optional
	Interval *metav1.Duration `protobuf:"bytes,2,opt,name=interval,proto3" json:"interval,omitempty"`

	// Minimum ejection duration. A host will remain ejected for a period
	// equal to the product of minimum ejection duration and the number of
	// times the host has been ejected. This technique allows the system to
	// automatically increase the ejection period for unhealthy upstream
	// servers. format: 1h/1m/1s/1ms. MUST BE >=1ms. Default is 30s.
	// +optional
	BaseEjectionTime *metav1.Duration `protobuf:"bytes,3,opt,name=baseEjectionTime,json=baseEjectionTime,proto3" json:"baseEjectionTime,omitempty"`

	// Maximum % of hosts in the load balancing pool for the upstream
	// service that can be ejected. Defaults to 10%.
	// +optional
	// +kubebuilder:validation:Maximum=100
	// +kubebuilder:validation:Minimum=1
	MaxEjectionPercent int32 `protobuf:"varint,4,opt,name=maxEjectionPercent,json=maxEjectionPercent,proto3" json:"maxEjectionPercent,omitempty"`
}

type HTTPRetry struct {
	// AutoRetire configures the service mesh to enable automatic retry.
	// +kubebuilder:default=false
	// +optional
	AutoRetire bool `json:"autoRetire,omitempty" protobuf:"bytes,1,opt,name=autoRetire"`

	// Number of retries to be allowed for a given request. The interval
	// between retries will be determined automatically (25ms+)
	// +kubebuilder:validation:Minimum=1
	// +kubebuilder:validation:Maximum=100
	// +optional
	Attempts int32 `protobuf:"varint,1,opt,name=attempts,proto3" json:"attempts,omitempty"`
	// Timeout per attempt for a given request, including the initial call and any retries. Format: 1h/1m/1s/1ms. MUST BE >=1ms.
	// Default is same value as request
	// +optional
	PerTryTimeout *metav1.Duration `protobuf:"bytes,5,opt,name=perTryTimeout,json=perTryTimeout,proto3" json:"perTryTimeout,omitempty"`
}

// GatewaySpec defines the specification of gateway.
type GatewaySpec struct {
	// Backend is the ServiceMesh project，set none means it will not be opened.
	// +kubebuilder:validation:Required
	Backend GatewayBackend `json:"backend,omitempty" protobuf:"bytes,1,opt,name=backend"`
	// Host is the host name of the gateway.
	// +kubebuilder:validation:Required
	Host string `json:"host,omitempty" protobuf:"bytes,2,opt,name=host"`
}

type HTTPMatchRequest struct {
	// URI is the path of the request. Exact match, prefix match and regex match are supported.
	// +optional
	URI *StringMatch `json:"uri,omitempty" protobuf:"bytes,2,opt,name=uri"`
	// Authority is the virtual host name of the request. Exact match, prefix match and regex match are supported.
	// +optional
	Authority *StringMatch `json:"authority,omitempty" protobuf:"bytes,3,opt,name=authority"`
	// Headers is the http headers list. Exact match, prefix match and regex match are supported.
	// +optional
	Headers map[string]*StringMatch `json:"headers,omitempty" protobuf:"bytes,4,opt,name=headers"`
}

type StringMatch struct {
	// Exact match
	// +optional
	Exact string `json:"exact,omitempty" protobuf:"bytes,1,opt,name=exact"`
	// Prefix match
	// +optional
	Prefix string `json:"prefix,omitempty" protobuf:"bytes,2,opt,name=prefix"`
	// Regex match
	// +optional
	Regex string `json:"regex,omitempty" protobuf:"bytes,3,opt,name=regex"`
}

type SchedulerSpec struct {
	// The minimal available pods to run for this Server
	// Defaults to the server replicas
	// +optional
	//+kubebuilder:validation:Minimum=0
	MinAvailable *int32 `json:"minAvailable,omitempty" protobuf:"bytes,4,opt,name=minAvailable"`

	//Specifies the queue that will be used in the scheduler, "default" queue is used this leaves empty.
	// +optional
	//+kubebuilder:default=default
	Queue string `json:"queue,omitempty" protobuf:"bytes,8,opt,name=queue"`

	// If specified, indicates the app's priority.
	// +optional
	PriorityClassName string `json:"priorityClassName,omitempty" protobuf:"bytes,9,opt,name=priorityClassName"`

	// Specifies the topology policy of server
	// +optional
	TopologyPolicy NumaPolicy `json:"topologyPolicy,omitempty" protobuf:"bytes,8,opt,name=topologyPolicy"`

	// MaxPendingDuration is the default value of `servers.template.spec.maxPendingDuration`.
	// +optional
	MaxPendingDuration *time.Duration `json:"maxPendingDuration,omitempty" protobuf:"bytes,10,opt,name=maxPendingDuration"`
	// MaxRetry is the default value of `servers.template.spec.maxRetry`.
	// +optional
	MaxRetry *int32 `json:"maxRetry,omitempty" protobuf:"bytes,11,opt,name=maxRetry"`

	// TODO single node
	// TODO server replicas anti-affinity
	// TODO server affinity
}

// VolumeSpec defines the specification of Volume, e.g. PVC.
type VolumeSpec struct {
	// Path within the container at which the volume should be mounted.  Must
	// not contain ':'.
	MountPath string `json:"mountPath" protobuf:"bytes,1,opt,name=mountPath"`

	// defined the PVC name
	// +optional
	VolumeClaimName string `json:"volumeClaimName,omitempty" protobuf:"bytes,2,opt,name=volumeClaimName"`

	// VolumeClaim defines the PVC used by the VolumeMount.
	// +optional
	VolumeClaim *v1.PersistentVolumeClaimSpec `json:"volumeClaim,omitempty" protobuf:"bytes,3,opt,name=volumeClaim"`
}

type ApplicationStatus struct {
	Phase ApplicationPhase `json:"phase,omitempty" protobuf:"bytes,1,opt,name=phase,casttype=ApplicationPhase"`

	Replicas int32 `json:"replicas,omitempty" protobuf:"bytes,2,opt,name=replicas"`

	// Total number of available servers (ready for at least minReadySeconds) targeted by this application.
	// +optional
	AvailableServers int32 `json:"availableServers,omitempty" protobuf:"bytes,4,opt,name=availableServers"`

	// Total number of available servers (ready for at least minReadySeconds) targeted by this application.
	// +optional
	FailedServers int32 `json:"failedServers,omitempty" protobuf:"bytes,4,opt,name=failedServers"`

	ListeningServers int32 `json:"listeningServers,omitempty" protobuf:"bytes,4,opt,name=listeningServers"`

	// Total number of available pods (ready for at least minReadySeconds) targeted by this application.
	// +optional
	AvailableReplicas int32 `json:"availableReplicas,omitempty" protobuf:"bytes,4,opt,name=availableReplicas"`
	// Represents the latest available observations of a deployment's current state.
	// +patchMergeKey=type
	// +patchStrategy=merge
	Conditions []ApplicationCondition `json:"conditions,omitempty" patchStrategy:"merge" patchMergeKey:"type" protobuf:"bytes,6,rep,name=conditions"`

	// ServerStates is a list of ServerState objects.
	// +optional
	ServerStates map[string]ServerState `json:"serverStates,omitempty" protobuf:"bytes,2,opt,name=serverStates"`
}

type ApplicationPhase string

const (
	// Creating is the phase that app is pending in the queue, waiting for scheduling decision
	Creating ApplicationPhase = "Creating"
	// Running is the phase that minimal available servers of app are running
	Running ApplicationPhase = "Running"
	// Stopping is the phase that the app is stopping, waiting for pod releasing
	Stopping ApplicationPhase = "Stopping"
	// Stopped is the phase that the app is stopped, waiting for pod releasing
	Stopped ApplicationPhase = "Stopped"
	// Terminating is the phase that the app is terminated, waiting for releasing pods
	Terminating ApplicationPhase = "Terminating"
	// Listening  is the phase during which the application scales down to zero
	Listening ApplicationPhase = "Listening"
	// Failed is the phase that the app is failed to be scheduled
	Failed ApplicationPhase = "Failed"
	// UnKnown is the phase that the app is unknown
	UnKnown ApplicationPhase = ""
)

type ApplicationConditionType string

// These are valid conditions of a application.
const (
	// ApplicationAvailable means the deployment is available, ie. at least the minimum available
	// replicas required are up and running for at least minReadySeconds.
	ApplicationAvailable ApplicationConditionType = "Available"
	// ApplicationProgressing means the deployment is progressing. Progress for a deployment is
	// considered when a new replica set is created or adopted, and when new pods scale
	// up or old pods scale down. Progress is not estimated for paused deployments or
	// when progressDeadlineSeconds is not specified.
	ApplicationProgressing ApplicationConditionType = "Progressing"
	// ApplicationReplicaFailure is added in a deployment when one of its pods fails to be created
	// or deleted.
	ApplicationReplicaFailure ApplicationConditionType = "ReplicaFailure"
)

// ApplicationCondition describes the state of a application at a certain point.
type ApplicationCondition struct {
	// Type of deployment condition.
	Type ApplicationConditionType `json:"type" protobuf:"bytes,1,opt,name=type,casttype=DeploymentConditionType"`
	// Status of the condition, one of True, False, Unknown.
	Status v1.ConditionStatus `json:"status" protobuf:"bytes,2,opt,name=status,casttype=k8s.io/api/core/v1.ConditionStatus"`
	// The last time this condition was updated.
	LastUpdateTime metav1.Time `json:"lastUpdateTime,omitempty" protobuf:"bytes,6,opt,name=lastUpdateTime"`
	// Last time the condition transitioned from one status to another.
	LastTransitionTime metav1.Time `json:"lastTransitionTime,omitempty" protobuf:"bytes,7,opt,name=lastTransitionTime"`
	// The reason for the condition's last transition.
	Reason string `json:"reason,omitempty" protobuf:"bytes,4,opt,name=reason"`
	// A human readable message indicating details about the transition.
	Message string `json:"message,omitempty" protobuf:"bytes,5,opt,name=message"`
}

// ServerState defines the observed state of Server.
type ServerState struct {

	// RetryNum is the number of retry.
	// +optional
	RetryNum int32 `json:"retryNum,omitempty" protobuf:"bytes,2,opt,name=retryNum"`

	// PendingDuration is the duration of pending.
	// +optional
	PendingDuration *time.Duration `json:"pendingDuration,omitempty" protobuf:"bytes,3,opt,name=pendingDuration"`

	// Phase is the state of the server.
	// +optional
	Phase ServerPhase `json:"phase,omitempty" protobuf:"bytes,4,opt,name=phase"`

	// Total number of pods (ready for at least minReadySeconds) targeted by this server.
	// +optional
	Replicas int32 `json:"replicas,omitempty" protobuf:"bytes,5,opt,name=replicas"`

	// Total number of available pods (ready for at least minReadySeconds) targeted by this server.
	// +optional
	AvailableReplicas int32 `json:"availableReplicas,omitempty" protobuf:"bytes,6,opt,name=availableReplicas"`
}

// ServerPhase defines the phase of the application.
type ServerPhase string

const (
	Ready           ServerPhase = "Ready"
	NotReady        ServerPhase = "NotReady"
	Available       ServerPhase = "Available"
	OutOfMaxPending ServerPhase = "OutOfMaxPending"
	OutOfMaxRetry   ServerPhase = "OutOfMaxRetry"
	Paused          ServerPhase = "Paused"
)

//+kubebuilder:object:root=true

// ApplicationList contains a list of Application
type ApplicationList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty"`
	Items           []Application `json:"items"`
}

func init() {
	SchemeBuilder.Register(&Application{}, &ApplicationList{})
}
