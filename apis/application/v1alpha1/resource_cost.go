/*
Copyright 2023.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package v1alpha1

import (
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

//+kubebuilder:object:root=true
//+kubebuilder:subresource:status
//+kubebuilder:resource:path=resourcecosts,shortName=rsct

// ResourceCost is the Schema for the resourceCosts API
type ResourceCost struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec   ResourceCostSpec   `json:"spec,omitempty"`
	Status ResourceCostStatus `json:"status,omitempty"`
}

// ResourceCostSpec defines the desired state of ResourceCost
type ResourceCostSpec struct {
	Current CurrentSpec `json:"current,omitempty"`
}

type CurrentSpec struct {
	Pods map[string][]*PodInfo `json:"pods,omitempty"`
}

type PodInfo struct {
	Name            string          `json:"name,omitempty"`
	ContainerName   string          `json:"containerName,omitempty"`
	Resource        v1.ResourceList `json:"resource,omitempty"`
	CreatingTime    *metav1.Time    `json:"creatingTime,omitempty"`
	RunningTime     *metav1.Time    `json:"runningTime,omitempty"`
	StoppedTime     *metav1.Time    `json:"stoppedTime,omitempty"`
	ContainerStatus string          `json:"containerStatus,omitempty"`
	NodeName        string          `json:"nodeName,omitempty"`
}

// ResourceCostStatus defines the observed state of ResourceCost
type ResourceCostStatus struct {
	Pods map[string][]*PodInfo `json:"pods,omitempty"`
}

//+kubebuilder:object:root=true

// ResourceCostList contains a list of ResourceCost
type ResourceCostList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty"`
	Items           []ResourceCost `json:"items"`
}

func init() {
	SchemeBuilder.Register(&ResourceCost{}, &ResourceCostList{})
}
