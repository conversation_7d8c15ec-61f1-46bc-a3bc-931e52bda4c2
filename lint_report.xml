<testsuites>
  <testsuite name="apis\application\v1alpha1\application_webhook.go" tests="2" errors="0" failures="2">
    <testcase name="goimports" classname="apis\application\v1alpha1\application_webhook.go:26">
      <failure message="apis\application\v1alpha1\application_webhook.go:26: File is not `goimports`-ed" type=""><![CDATA[: File is not `goimports`-ed
Category: goimports
File: apis\application\v1alpha1\application_webhook.go
Line: 26
Details: 	"gitlab.leinao.ai/application-controller/pkg/auth_manager"]]></failure>
    </testcase>
    <testcase name="nestif" classname="apis\application\v1alpha1\application_webhook.go:288:2">
      <failure message="apis\application\v1alpha1\application_webhook.go:288:2: `if tp != nil` has complex nested blocks (complexity: 11)" type=""><![CDATA[: `if tp != nil` has complex nested blocks (complexity: 11)
Category: nestif
File: apis\application\v1alpha1\application_webhook.go
Line: 288
Details: 	if tp := sm.SubRoute.TrafficPolicy; tp != nil {]]></failure>
    </testcase>
  </testsuite>
  <testsuite name="cmd\controller-manager\app\options\options.go" tests="1" errors="0" failures="1">
    <testcase name="goimports" classname="cmd\controller-manager\app\options\options.go:5">
      <failure message="cmd\controller-manager\app\options\options.go:5: File is not `goimports`-ed" type=""><![CDATA[: File is not `goimports`-ed
Category: goimports
File: cmd\controller-manager\app\options\options.go
Line: 5
Details: 	"fmt"]]></failure>
    </testcase>
  </testsuite>
  <testsuite name="pkg\auth_manager\client.go" tests="1" errors="0" failures="1">
    <testcase name="golint" classname="pkg\auth_manager\client.go:76:2">
      <failure message="pkg\auth_manager\client.go:76:2: var `authUrl` should be `authURL`" type=""><![CDATA[: var `authUrl` should be `authURL`
Category: golint
File: pkg\auth_manager\client.go
Line: 76
Details: 	authUrl := s.authURL + "/license/check/expire"]]></failure>
    </testcase>
  </testsuite>
  <testsuite name="pkg\backend\assembly\istio\istio.go" tests="1" errors="0" failures="1">
    <testcase name="nestif" classname="pkg\backend\assembly\istio\istio.go:123:3">
      <failure message="pkg\backend\assembly\istio\istio.go:123:3: `if ser.ServiceMesh.SubRoute != nil &amp;&amp; ser.ServiceMesh.SubRoute.TrafficPolicy != nil` has complex nested blocks (complexity: 15)" type=""><![CDATA[: `if ser.ServiceMesh.SubRoute != nil && ser.ServiceMesh.SubRoute.TrafficPolicy != nil` has complex nested blocks (complexity: 15)
Category: nestif
File: pkg\backend\assembly\istio\istio.go
Line: 123
Details: 		if ser.ServiceMesh.SubRoute != nil && ser.ServiceMesh.SubRoute.TrafficPolicy != nil {]]></failure>
    </testcase>
  </testsuite>
  <testsuite name="pkg\backend\empty\empty.go" tests="1" errors="0" failures="1">
    <testcase name="golint" classname="pkg\backend\empty\empty.go:9:6">
      <failure message="pkg\backend\empty\empty.go:9:6: type name will be used as empty.EmptyBackend by other packages, and that stutters; consider calling this Backend" type=""><![CDATA[: type name will be used as empty.EmptyBackend by other packages, and that stutters; consider calling this Backend
Category: golint
File: pkg\backend\empty\empty.go
Line: 9
Details: type EmptyBackend struct {]]></failure>
    </testcase>
  </testsuite>
  <testsuite name="pkg\backend\resource_cost\resource_cost.go" tests="1" errors="0" failures="1">
    <testcase name="golint" classname="pkg\backend\resource_cost\resource_cost.go:1:1">
      <failure message="pkg\backend\resource_cost\resource_cost.go:1:1: don&#39;t use an underscore in package name" type=""><![CDATA[: don't use an underscore in package name
Category: golint
File: pkg\backend\resource_cost\resource_cost.go
Line: 1
Details: package resource_cost]]></failure>
    </testcase>
  </testsuite>
  <testsuite name="pkg\config\config.go" tests="3" errors="0" failures="3">
    <testcase name="golint" classname="pkg\config\config.go:18:2">
      <failure message="pkg\config\config.go:18:2: var `svcDataId` should be `svcDataID`" type=""><![CDATA[: var `svcDataId` should be `svcDataID`
Category: golint
File: pkg\config\config.go
Line: 18
Details: 	svcDataId  = "application-controller-config.yaml"]]></failure>
    </testcase>
    <testcase name="unused" classname="pkg\config\config.go:20:2">
      <failure message="pkg\config\config.go:20:2: var `namespace` is unused" type=""><![CDATA[: var `namespace` is unused
Category: unused
File: pkg\config\config.go
Line: 20
Details: 	namespace  = "hero-user"]]></failure>
    </testcase>
    <testcase name="unused" classname="pkg\config\config.go:42:24">
      <failure message="pkg\config\config.go:42:24: func `(*ServerConfig).getNamespace` is unused" type=""><![CDATA[: func `(*ServerConfig).getNamespace` is unused
Category: unused
File: pkg\config\config.go
Line: 42
Details: func (s *ServerConfig) getNamespace() string {]]></failure>
    </testcase>
  </testsuite>
  <testsuite name="pkg\controller\application_controller.go" tests="5" errors="0" failures="5">
    <testcase name="golint" classname="pkg\controller\application_controller.go:49:2">
      <failure message="pkg\controller\application_controller.go:49:2: const `LabelUserIdKey` should be `LabelUserIDKey`" type=""><![CDATA[: const `LabelUserIdKey` should be `LabelUserIDKey`
Category: golint
File: pkg\controller\application_controller.go
Line: 49
Details: 	LabelUserIdKey = "leinao.ai/user-id"]]></failure>
    </testcase>
    <testcase name="nestif" classname="pkg\controller\application_controller.go:137:2">
      <failure message="pkg\controller\application_controller.go:137:2: `if app.Status.Phase == &#34;&#34;` has complex nested blocks (complexity: 50)" type=""><![CDATA[: `if app.Status.Phase == ""` has complex nested blocks (complexity: 50)
Category: nestif
File: pkg\controller\application_controller.go
Line: 137
Details: 	if app.Status.Phase == "" {]]></failure>
    </testcase>
    <testcase name="unused" classname="pkg\controller\application_controller.go:323:33">
      <failure message="pkg\controller\application_controller.go:323:33: func `(*ApplicationReconciler).updateApplication` is unused" type=""><![CDATA[: func `(*ApplicationReconciler).updateApplication` is unused
Category: unused
File: pkg\controller\application_controller.go
Line: 323
Details: func (r *ApplicationReconciler) updateApplication(ctx context.Context, app *systemv1alpha1.Application) (ctrl.Result, error) {]]></failure>
    </testcase>
    <testcase name="cyclop" classname="pkg\controller\application_controller.go:87:1">
      <failure message="pkg\controller\application_controller.go:87:1: calculated cyclomatic complexity for function Reconcile is 59, max is 15" type=""><![CDATA[: calculated cyclomatic complexity for function Reconcile is 59, max is 15
Category: cyclop
File: pkg\controller\application_controller.go
Line: 87
Details: func (r *ApplicationReconciler) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) {]]></failure>
    </testcase>
    <testcase name="cyclop" classname="pkg\controller\application_controller.go:330:1">
      <failure message="pkg\controller\application_controller.go:330:1: calculated cyclomatic complexity for function getAllBackends is 16, max is 15" type=""><![CDATA[: calculated cyclomatic complexity for function getAllBackends is 16, max is 15
Category: cyclop
File: pkg\controller\application_controller.go
Line: 330
Details: func getAllBackends(app *systemv1alpha1.Application) []api.BackendInterface {]]></failure>
    </testcase>
  </testsuite>
  <testsuite name="pkg\controller\application_event.go" tests="3" errors="0" failures="3">
    <testcase name="gofmt" classname="pkg\controller\application_event.go:1">
      <failure message="pkg\controller\application_event.go:1: File is not `gofmt`-ed with `-s`" type=""><![CDATA[: File is not `gofmt`-ed with `-s`
Category: gofmt
File: pkg\controller\application_event.go
Line: 1
Details: package controller

import (
	"context"
	"fmt"
	"sync"
	"time"

	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/klog"

	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	ref "k8s.io/client-go/tools/reference"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

var (
	rd          *removeDup
	idleTimeout = 300 * time.Second
	timeCheck   = 60 * time.Second
	prefix      = "job-"
)

type removeDup struct {
	data    map[string]time.Time
	timeout time.Duration
	mutex   sync.Mutex
	timer   *time.Timer
}

func NewRemoveDup(timeout time.Duration) *removeDup {
	rp := &removeDup{
		data:    make(map[string]time.Time),
		timeout: timeout,
	}

	rp.startClean()
	return rp
}

func (rd *removeDup) Add(key string) {
	rd.mutex.Lock()
	defer rd.mutex.Unlock()
	rd.data[key] = time.Now().Add(rd.timeout)
}

func (rd *removeDup) Exists(key string) bool {
	rd.mutex.Lock()
	defer rd.mutex.Unlock()
	if _, ok := rd.data[key]; !ok {
		return false
	}

	return true
}

func (rd *removeDup) startClean() {
	rd.timer = time.NewTimer(timeCheck)
	go func() {
		for {
			<-rd.timer.C
			rd.mutex.Lock()
			expiredKeys := make([]string, 0)
			now := time.Now()
			for key, expiration := range rd.data {
				if now.After(expiration) {
					expiredKeys = append(expiredKeys, key)
				}
			}
			for _, key := range expiredKeys {
				delete(rd.data, key)
			}
			rd.mutex.Unlock()
			rd.timer.Reset(timeCheck)
		}
	}()
}

func init() {
	rd = NewRemoveDup(idleTimeout)
}

func NewEventRecord(cli client.Client, scheme *runtime.Scheme) *eventRecord {
	return &eventRecord{
		client: cli,
		scheme: scheme,
	}
}

type eventRecord struct {
	client client.Client
	scheme *runtime.Scheme
	reason string
}

func (e *eventRecord) eventRecord(ctx context.Context, obj client.Object, eventtype, reason, message string) error {
	if !rd.Exists(prefix + obj.GetName() + reason + message + e.reason) {
		rd.Add(prefix + obj.GetName() + reason + message + e.reason)

		ref, err := ref.GetReference(e.scheme, obj)
		if err != nil {
			klog.Errorf("%s getReference failed: %s", obj.GetObjectKind().GroupVersionKind().Kind, err.Error())
			return err
		}
		evt := e.makeEvent(ref, nil, eventtype, reason, message, obj.GetObjectKind().GroupVersionKind().Kind)
		return e.client.Create(ctx, evt)
	}

	klog.Infof("job %s already exists", obj.GetName())
	return nil
}

func (e *eventRecord) makeEvent(ref *v1.ObjectReference, annotations map[string]string, eventtype, reason, message, reskind string) *v1.Event {
	t := metav1.Time{Time: time.Now()}
	namespace := ref.Namespace
	if namespace == "" {
		namespace = metav1.NamespaceDefault
	}
	return &v1.Event{
		ObjectMeta: metav1.ObjectMeta{
			Name:        fmt.Sprintf("%v.%x", ref.Name, t.UnixNano()),
			Namespace:   namespace,
			Annotations: annotations,
			Labels:      map[string]string{"system.hero.ai": "event"},
		},
		InvolvedObject: *ref,
		Reason:         reason,
		Message:        message,
		FirstTimestamp: t,
		LastTimestamp:  t,
		Type:           eventtype,
		Source: v1.EventSource{
			Component: reskind,
		},
	}
}]]></failure>
    </testcase>
    <testcase name="golint" classname="pkg\controller\application_event.go:32:42">
      <failure message="pkg\controller\application_event.go:32:42: exported func NewRemoveDup returns unexported type *gitlab.leinao.ai/application-controller/pkg/controller.removeDup, which can be annoying to use" type=""><![CDATA[: exported func NewRemoveDup returns unexported type *gitlab.leinao.ai/application-controller/pkg/controller.removeDup, which can be annoying to use
Category: golint
File: pkg\controller\application_event.go
Line: 32
Details: func NewRemoveDup(timeout time.Duration) *removeDup {]]></failure>
    </testcase>
    <testcase name="golint" classname="pkg\controller\application_event.go:84:64">
      <failure message="pkg\controller\application_event.go:84:64: exported func NewEventRecord returns unexported type *gitlab.leinao.ai/application-controller/pkg/controller.eventRecord, which can be annoying to use" type=""><![CDATA[: exported func NewEventRecord returns unexported type *gitlab.leinao.ai/application-controller/pkg/controller.eventRecord, which can be annoying to use
Category: golint
File: pkg\controller\application_event.go
Line: 84
Details: func NewEventRecord(cli client.Client, scheme *runtime.Scheme) *eventRecord {]]></failure>
    </testcase>
  </testsuite>
  <testsuite name="pkg\controller\resourcecost_controller.go" tests="5" errors="0" failures="5">
    <testcase name="golint" classname="pkg\controller\resourcecost_controller.go:131:4">
      <failure message="pkg\controller\resourcecost_controller.go:131:4: var `appJobId` should be `appJobID`" type=""><![CDATA[: var `appJobId` should be `appJobID`
Category: golint
File: pkg\controller\resourcecost_controller.go
Line: 131
Details: 			appJobId := app.GetAnnotations()[systemv1alpha1.LabelApplicationJobID]]]></failure>
    </testcase>
    <testcase name="golint" classname="pkg\controller\resourcecost_controller.go:183:3">
      <failure message="pkg\controller\resourcecost_controller.go:183:3: var `appJobId` should be `appJobID`" type=""><![CDATA[: var `appJobId` should be `appJobID`
Category: golint
File: pkg\controller\resourcecost_controller.go
Line: 183
Details: 		appJobId := app.GetAnnotations()[systemv1alpha1.LabelApplicationJobID]]]></failure>
    </testcase>
    <testcase name="cyclop" classname="pkg\controller\resourcecost_controller.go:169:1">
      <failure message="pkg\controller\resourcecost_controller.go:169:1: calculated cyclomatic complexity for function statPodHistorys is 16, max is 15" type=""><![CDATA[: calculated cyclomatic complexity for function statPodHistorys is 16, max is 15
Category: cyclop
File: pkg\controller\resourcecost_controller.go
Line: 169
Details: func (r *ResourceCostReconciler) statPodHistorys(rsct *systemv1alpha1.ResourceCost, pods *corev1.PodList, app *systemv1alpha1.Application) *systemv1alpha1.ResourceCostStatus {]]></failure>
    </testcase>
    <testcase name="cyclop" classname="pkg\controller\resourcecost_controller.go:463:1">
      <failure message="pkg\controller\resourcecost_controller.go:463:1: calculated cyclomatic complexity for function updateServerStatus is 22, max is 15" type=""><![CDATA[: calculated cyclomatic complexity for function updateServerStatus is 22, max is 15
Category: cyclop
File: pkg\controller\resourcecost_controller.go
Line: 463
Details: func (r *ResourceCostReconciler) updateServerStatus(status map[string]systemv1alpha1.ServerState, app *systemv1alpha1.Application) {]]></failure>
    </testcase>
    <testcase name="cyclop" classname="pkg\controller\resourcecost_controller.go:538:1">
      <failure message="pkg\controller\resourcecost_controller.go:538:1: calculated cyclomatic complexity for function getPodContainerMainStatus is 38, max is 15" type=""><![CDATA[: calculated cyclomatic complexity for function getPodContainerMainStatus is 38, max is 15
Category: cyclop
File: pkg\controller\resourcecost_controller.go
Line: 538
Details: func getPodContainerMainStatus(pod *corev1.Pod) (string, error) {]]></failure>
    </testcase>
  </testsuite>
  <testsuite name="pkg\informers\informers.go" tests="1" errors="0" failures="1">
    <testcase name="golint" classname="pkg\informers\informers.go:67:29">
      <failure message="pkg\informers\informers.go:67:29: method ApiExtensionSharedInformerFactory should be APIExtensionSharedInformerFactory" type=""><![CDATA[: method ApiExtensionSharedInformerFactory should be APIExtensionSharedInformerFactory
Category: golint
File: pkg\informers\informers.go
Line: 67
Details: func (f *informerFactories) ApiExtensionSharedInformerFactory() apiextensionsinformers.SharedInformerFactory {]]></failure>
    </testcase>
  </testsuite>
  <testsuite name="pkg\simple\client\k8s\kubernetes.go" tests="1" errors="0" failures="1">
    <testcase name="golint" classname="pkg\simple\client\k8s\kubernetes.go:88:28">
      <failure message="pkg\simple\client\k8s\kubernetes.go:88:28: method ApiExtensions should be APIExtensions" type=""><![CDATA[: method ApiExtensions should be APIExtensions
Category: golint
File: pkg\simple\client\k8s\kubernetes.go
Line: 88
Details: func (k *kubernetesClient) ApiExtensions() apiextensionsclient.Interface {]]></failure>
    </testcase>
  </testsuite>
  <testsuite name="pkg\utils\controller\controllerutil.go" tests="1" errors="0" failures="1">
    <testcase name="goimports" classname="pkg\utils\controller\controllerutil.go:5">
      <failure message="pkg\utils\controller\controllerutil.go:5: File is not `goimports`-ed" type=""><![CDATA[: File is not `goimports`-ed
Category: goimports
File: pkg\utils\controller\controllerutil.go
Line: 5
Details: 	"fmt"]]></failure>
    </testcase>
  </testsuite>
  <testsuite name="pkg\utils\reflectutils\deep.go" tests="1" errors="0" failures="1">
    <testcase name="cyclop" classname="pkg\utils\reflectutils\deep.go:76:1">
      <failure message="pkg\utils\reflectutils\deep.go:76:1: calculated cyclomatic complexity for function equals is 77, max is 15" type=""><![CDATA[: calculated cyclomatic complexity for function equals is 77, max is 15
Category: cyclop
File: pkg\utils\reflectutils\deep.go
Line: 76
Details: func (c *cmp) equals(a, b reflect.Value, level int) {]]></failure>
    </testcase>
  </testsuite>
</testsuites>