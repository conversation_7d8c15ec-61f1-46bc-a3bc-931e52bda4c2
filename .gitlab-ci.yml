image:
  name: "registry.cnbita.com:5000/golangci/golangci-lint-go1.20.2-gitlab:v1.52.0"
  pull_policy: if-not-present

stages:
  - build
  - sonarqube-check
  - lint
  - test

go-build:
  stage: build
  script:
    - make build
  only:
    - dev
    - merge_requests

sonarqube-check:
  stage: sonarqube-check
  image:
    name: registry.cnbita.com:5000/golangci/sonar-scanner-cli-go122:v1
    pull_policy: if-not-present
    entrypoint: [""]
  variables:
    SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"  # Defines the location of the analysis task cache
    GIT_DEPTH: "0"  # Tells git to fetch all the branches of the project, required by the analysis task
    SONAR_HOST_URL: "${SONAR_HOST_URL}"
    SONAR_TOKEN: "${SONAR_TOKEN}"
    SONAR_PROJECT_KEY: "${SONAR_PROJECT_KEY}"
    NACOS_SERVER_URL: ***********:38848
    INIT_MODE: nacos
    JASYPT_ENCRYPTOR_PASSWORD: 123#lei@nao@ai@#123
    LOCALBIN: ${CI_PROJECT_DIR}/bin  #使用绝对路径
    ENVTEST_K8S_VERSION: 1.28.0
  allow_failure: true
  coverage: '/^total:\s*\(statements\)\s*([\d\.]+)%/'
  before_script:
    - mkdir -p ${LOCALBIN}  # 创建本地二进制文件存放路径
    - GOBIN=${LOCALBIN} go install sigs.k8s.io/controller-runtime/tools/setup-envtest@release-0.17  # 安装 setup-envtest 工具
    - ${LOCALBIN}/setup-envtest use ${ENVTEST_K8S_VERSION} --bin-dir ${LOCALBIN}  # 下载 Kubernetes 环境
    - chmod +x -R ${LOCALBIN} && chmod +x -R ${LOCALBIN}/k8s/${ENVTEST_K8S_VERSION}-linux-amd64  # 设置执行权限
  script:
    - bash ./sonar/sonarqube-check.sh
    - |
      sonar-scanner \
        -Dsonar.host.url=$SONAR_HOST_URL \
        -Dsonar.login=$SONAR_TOKEN \
        -Dsonar.projectKey=$SONAR_PROJECT_KEY \
        -Dsonar.branch.name=$CI_COMMIT_REF_NAME \
        -X
  rules:
    - if: '$CI_PIPELINE_SOURCE == "schedule"'
      when: always

go-lint:
  stage: lint
  script:
    - golangci-lint run -c ./.golangci.yml ./... --verbose --out-format junit-xml > lint_report.xml
  allow_failure: true
  artifacts:
    paths:
      - lint_report.xml
    reports:
      junit: lint_report.xml
  only:
    - dev
    - test-ci
    - merge_requests

go-unittest:
  stage: test
  variables:
    NACOS_SERVER_URL: ***********:38848
    INIT_MODE: nacos
    JASYPT_ENCRYPTOR_PASSWORD: 123#lei@nao@ai@#123
    LOCALBIN: ${CI_PROJECT_DIR}/bin  #使用绝对路径
    ENVTEST_K8S_VERSION: 1.28.0

  before_script:
    - mkdir -p ${LOCALBIN}  # 创建本地二进制文件存放路径
    - GOBIN=${LOCALBIN} go install sigs.k8s.io/controller-runtime/tools/setup-envtest@release-0.17  # 安装 setup-envtest 工具
    - ${LOCALBIN}/setup-envtest use ${ENVTEST_K8S_VERSION} --bin-dir ${LOCALBIN}  # 下载 Kubernetes 环境
    - chmod +x -R ${LOCALBIN} && chmod +x -R ${LOCALBIN}/k8s/${ENVTEST_K8S_VERSION}-linux-amd64  # 设置执行权限

  script:
    - go test ./... -v -coverprofile=coverage.out  # 运行所有单元测试并生成覆盖率文件
    - go tool cover -func=coverage.out  # 输出测试覆盖率的详细报告
    - go tool cover -html=coverage.out -o coverage.html  # 生成 HTML 格式的覆盖率报告
    - go install gotest.tools/gotestsum@latest
    - gotestsum --junitfile report.xml --format standard-verbose

  artifacts:
    when: always
    reports:
      junit: report.xml
    paths:
      - coverage.out
      - coverage.html
    expire_in: 15 day
  coverage: '/^total:\s*\(statements\)\s*([\d\.]+)%/'
  only:
    - dev
    - merge_requests